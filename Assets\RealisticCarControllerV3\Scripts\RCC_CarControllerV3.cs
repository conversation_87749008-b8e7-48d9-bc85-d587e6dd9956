//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2019 BoneCracker Games
// http://www.bonecrackergames.com
// <PERSON><PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using UnityEngine.Audio;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.EventSystems;

[AddComponentMenu("BoneCracker Games/Realistic Car Controller/Main/RCC Realistic Car Controller V3")]
[RequireComponent (typeof(Rigidbody))]
/// <summary>
/// Main vehicle controller that includes Wheels, Steering, Suspensions, Mechanic Configuration, Stability, Lights, Sounds, and Damage.
/// </summary>
public class RCC_CarControllerV3 : MonoBehaviour {

	// Getting an Instance of Main Shared RCC Settings.
	#region RCC Settings Instance

	private RCC_Settings RCCSettingsInstance;
	private RCC_Settings RCCSettings {
		get {
			if (RCCSettingsInstance == null) {
				RCCSettingsInstance = RCC_Settings.Instance;
				return RCCSettingsInstance;
			}
			return RCCSettingsInstance;
		}
	}

	#endregion

	internal Rigidbody rigid;							// Rigidbody.
	internal bool isSleeping = false;				// Used For Disabling Unnecessary Raycasts When RB Is Sleeping.
	public bool externalController = false;		// Use AI Controller.

	[Obsolete("Warning 'AIController' is obsolete: 'Please use externalController.")]
	public bool AIController { get { return this.externalController; } set { this.externalController = value; } }
	
	// Wheel Transforms Of The Vehicle.
	public Transform FrontLeftWheelTransform;
	public Transform FrontRightWheelTransform;
	public Transform RearLeftWheelTransform;
	public Transform RearRightWheelTransform;
	
	// Wheel Colliders Of The Vehicle.
	public RCC_WheelCollider FrontLeftWheelCollider;
	public RCC_WheelCollider FrontRightWheelCollider;
	public RCC_WheelCollider RearLeftWheelCollider;
	public RCC_WheelCollider RearRightWheelCollider;

	// All Wheel Colliders.
	internal RCC_WheelCollider[] allWheelColliders;
	
	// Extra Wheels. In case of if your vehicle has extra wheels.
	public Transform[] ExtraRearWheelsTransform;
	public RCC_WheelCollider[] ExtraRearWheelsCollider;
	public bool applyEngineTorqueToExtraRearWheelColliders = true;	//Applies Engine Torque To Extra Rear Wheels.

	// Steering wheel model.
	public Transform SteeringWheel;														// Driver Steering Wheel. In case of if your vehicle has individual steering wheel model in interior.
	private Quaternion orgSteeringWheelRot;											// Original rotation of Steering Wheel.
	public SteeringWheelRotateAround steeringWheelRotateAround;		// Current rotation of Steering Wheel.
	public enum SteeringWheelRotateAround { XAxis, YAxis, ZAxis }		//	Rotation axis of Steering Wheel.
	public float steeringWheelAngleMultiplier = 3f;								// Angle multiplier of Steering Wheel.
	
	// Set wheel drive of the vehicle. If you are using rwd, you have to be careful with your rear wheel collider
	// settings and com of the vehicle. Otherwise, vehicle will behave like a toy.
	public WheelType wheelTypeChoise = WheelType.RWD;
	public enum WheelType{FWD, RWD, AWD, BIASED}
	[Range(0f, 100f)]public float biasedWheelTorque = 100f;

	public Transform COM;		// Center of mass.

	// Bools.
	public bool canControl = true;																			// Enables / Disables controlling the vehicle.
	public bool runEngineAtAwake{get{return RCCSettings.runEngineAtAwake;}}		// Engine running at Awake?
	public bool engineRunning = false;																		// Engine running now?
	public bool autoReverse{get{if(!externalController) return RCCSettings.autoReverse; else return true;}}                            // Enables / Disables auto reversing when player press brake button. Useful for if you are making parking style game.
	public bool automaticGear{get{if(!externalController) return RCCSettings.useAutomaticGear; else return true;}}                // Enables / Disables automatic gear shifting of the vehicle.
	internal bool semiAutomaticGear = false;															// Enables / Disables automatic gear shifting of the vehicle.
	internal bool canGoReverseNow = false;
	 
	// Configurations.
	public AnimationCurve[] engineTorqueCurve;				// Each Gear Ratio Curves Generated By Editor Script.
	public float finalRatio = 3.23f;									//	Final Drive Gear Ratio. 
	public float engineTorque = 150f;								// Default Engine Torque.
	public float brakeTorque = 2000f;							// Maximum Brake Torque.,
	public float minEngineRPM = 1000f;							// Minimum Engine RPM.
	public float maxEngineRPM = 7000f;							// Maximum Engine RPM.
	[Range(.75f, 2f)]public float engineInertia = 1f;		// Inertia of the engine.
	public bool useRevLimiter = true;								// Rev Limiter above Maximum Engine RPM.
	public bool useExhaustFlame = true;							// Exhaust blow flame.
	public bool useClutchMarginAtFirstGear = true;			// Smooth clutching at first gear.
	public float steerAngle = 40f;									// Maximum Steer Angle Of Your Vehicle.
	public float highspeedsteerAngle = 3f;						// Maximum Steer Angle At Highest Speed.
	public float highspeedsteerAngleAtspeed = 200f;		// Highest Speed For Maximum Steer Angle.
	[Range(1f, 15f)]public float steeringSensitivity = 5f;// Steering Sensitivity.
	public float antiRollFrontHorizontal = 1000f;			// Anti Roll Horizontal Force For Preventing Flip Overs And Stability.
	public float antiRollRearHorizontal = 1000f;				// Anti Roll Horizontal Force For Preventing Flip Overs And Stability.
	public float antiRollVertical = 0f;								// Anti Roll Vertical Force For Preventing Flip Overs And Stability. I know it doesn't exist, but it can improve SMGGameManager if you have height COM vehicles like monster trucks.

	// Downforce.
	public float downForce = 25f;		// Applies downforce related with vehicle speed.

	public float speed = 0f;				// Vehicle speed.
	public float orgMaxSpeed = 0f;	// Original maximum speed.
	public float maxspeed = 220f;		// Maximum speed.

	private float resetTime = 0f;					// Used for resetting the vehicle if upside down.
	private float orgSteerAngle = 0f;			// Original steer angle.

	// Fuel.
	public bool useFuelConsumption = false;		// Enable / Disable Fuel Consumption.
	public float fuelTankCapacity = 62f;			// Fuel Tank Capacity.
	public float fuelTank = 62f;							// Fuel Amount.
	public float fuelConsumptionRate = .1f;			// Fuel Consumption Rate.

	// Engine heat.
	public bool useEngineHeat = false;							// Enable / Disable engine heat.
	public float engineHeat = 15f;									// Engine heat.
	public float engineCoolingWaterThreshold = 60f;		// Engine coolign water engage point.
	public float engineHeatRate = 1f;								// Engine heat multiplier.
	public float engineCoolRate = 1f;								// Engine cool multiplier.

	// Gears.
	[System.Serializable]
	public class Gear{

		public float maxRatio;
		public int maxSpeed;
		public int targetSpeedForNextGear;
		public AnimationCurve torqueCurve;

		public void SetGear(float ratio, int speed, int targetSpeed){

			maxRatio = ratio;
			maxSpeed = speed;
			targetSpeedForNextGear = targetSpeed;
			
		}

	}

	public Gear[] gears;

	public int totalGears = 6;			//	Total count of gears.
	public int currentGear = 0;		// Current Gear Of The Vehicle.
	[Range(0f, .5f)]public float gearShiftingDelay = .35f;
	[Range(.25f, .8f)]public float gearShiftingThreshold = .75f;
	[Range(.1f, .9f)]public float clutchInertia = .25f;
	private float orgGearShiftingThreshold;		// Original Gear Shifting Threshold.
	public bool changingGear = false;		// Changing Gear Currently.
	public bool NGear = false;		// N Gear.
	public int direction = 1;		// Reverse Gear Currently.
	public float launched = 0f;

	public bool autoGenerateGearCurves = true;		// Each gear has it's own torque curve.
	public bool autoGenerateTargetSpeedsForChangingGear = true;		// Target speeds for shifting between gears.
	
	// How many source we will use for simulating engine sounds?. Usually, all modern driving games have around 6 audio sources per vehicle.
	// Low RPM, Medium RPM, and High RPM. And their off versions.
	public AudioType audioType;
	public enum AudioType{OneSource, TwoSource, ThreeSource, Off}

	// If you don't have their off versions, generate them.
	public bool autoCreateEngineOffSounds = true;

	// AudioSources and AudioClips.
	private AudioSource engineStartSound;
	public AudioClip engineStartClip;
	internal AudioSource engineSoundHigh;
	public AudioClip engineClipHigh;
	private AudioSource engineSoundMed;
	public AudioClip engineClipMed;
	private AudioSource engineSoundLow;
	public AudioClip engineClipLow;
	private AudioSource engineSoundIdle;
	public AudioClip engineClipIdle;
	private AudioSource gearShiftingSound;

	internal AudioSource engineSoundHighOff;
	public AudioClip engineClipHighOff;
	internal AudioSource engineSoundMedOff;
	public AudioClip engineClipMedOff;
	internal AudioSource engineSoundLowOff;
	public AudioClip engineClipLowOff;

	// Shared AudioSources and AudioClips.
	private AudioClip[] gearShiftingClips{get{return RCCSettings.gearShiftingClips;}}
	private AudioSource crashSound;
	private AudioClip[] crashClips{get{return RCCSettings.crashClips;}}
	private AudioSource reversingSound;
	private AudioClip reversingClip{get{return RCCSettings.reversingClip;}}
	private AudioSource windSound;
	private AudioClip windClip{get{return RCCSettings.windClip;}}
	private AudioSource brakeSound;
	private AudioClip brakeClip{get{return RCCSettings.brakeClip;}}
	private AudioSource NOSSound;
	private AudioClip NOSClip{get{return RCCSettings.NOSClip;}}
	private AudioSource turboSound;
	private AudioClip turboClip{get{return RCCSettings.turboClip;}}
	private AudioSource blowSound;
	private AudioClip blowClip{get{return RCCSettings.turboClip;}}

	// Min / Max sound pitches and volumes.
	[Range(0f, 1f)]public float minEngineSoundPitch = .75f;
	[Range(1f, 2f)]public float maxEngineSoundPitch = 1.75f;
	[Range(0f, 1f)]public float minEngineSoundVolume = .05f;
	[Range(0f, 1f)]public float maxEngineSoundVolume = .85f;
	[Range(0f, 1f)]public float idleEngineSoundVolume = .85f;

	// Audio balance settings for high speed
	public float audioBalanceSpeedThreshold = 150f; // Speed at which audio balance starts
	public float highSpeedVolumeReduction = 0.7f; // Volume reduction at high speeds

	// Pitch control settings
	public float maxPitchAtHighSpeed = 1.4f; // Maximum pitch limit at high speeds (instead of 1.75f)
	public float pitchSmoothingFactor = 0.8f; // How smooth the pitch transition should be

	// Main Gameobjects for keep the Hierarchy clean and organized.
	private GameObject allContactParticles;
	
	// Inputs.
	[HideInInspector]public float gasInput = 0f;
	[HideInInspector]public float brakeInput = 0f;
	[HideInInspector]public float steerInput = 0f;
	[HideInInspector]public float clutchInput = 0f;
	[HideInInspector]public float handbrakeInput = 0f;
	[HideInInspector]public float boostInput = 0f;
	[HideInInspector]public float idleInput = 0f;
	[HideInInspector]public float fuelInput = 0f;
	[HideInInspector]public bool cutGas = false;

	private bool permanentGas = false;

	#region Processed Inputs
	// Processed Inputs. DO NOT FEED THESE VALUES on your own script. Feed only above inputs.
	internal float _gasInput{get{

			if(_fuelInput <= 0f)
				return 0f;

			if(!automaticGear || semiAutomaticGear){
				if(!changingGear && !cutGas)
					return Mathf.Clamp01(gasInput);
				else
					return 0f;
			}else{
				if(!changingGear && !cutGas)
					return (direction == 1 ? Mathf.Clamp01(gasInput) : Mathf.Clamp01(brakeInput));
				else
					return 0f;
			}
				
		}set{gasInput = value;}}

	internal float _brakeInput{get{

			if(!automaticGear || semiAutomaticGear){
				return Mathf.Clamp01(brakeInput);
			}else{
				if(!cutGas)
					return (direction == 1 ? Mathf.Clamp01(brakeInput) : Mathf.Clamp01(gasInput));
				else
					return 0f;
			}
				
		}set{brakeInput = value;}}

	internal float _boostInput{get{
			
			if(useNOS && NoS > 5 && _gasInput >= .5f){
				return boostInput * 1f;
			}else{
				return 0f;
			}

		}set{boostInput = value;}}

	internal float _steerInput{get{

			return (steerInput + _counterSteerInput);

		}}

	internal float _counterSteerInput{get{

			if (applyCounterSteering)
				return (driftAngle * counterSteeringFactor);
			else
				return 0f;

		}}

	internal float _fuelInput{get{

			if(fuelTank > 0){
				return fuelInput;
			}else{
				if(engineRunning)
					KillEngine ();
				return 0f;
			}

		}set{fuelInput = value;}}

	#endregion

	internal float rawEngineRPM = 0f;	// Actual engine RPM.
	internal float engineRPM = 0f;			// Smoothed engine RPM.
	
	public GameObject chassis;						// Script will simulate chassis movement based on vehicle rigidbody velocity.
	public float chassisVerticalLean = 4f;		// Chassis vertical lean sensitivity.
	public float chassisHorizontalLean = 4f;	// Chassis horizontal lean sensitivity.
	
	// Lights.
	public bool lowBeamHeadLightsOn = false;	// Low beam head lights.
	public bool highBeamHeadLightsOn = false;	// High beam head lights.

	// For Indicators.
	public IndicatorsOn indicatorsOn;		// Indicator system.
	public enum IndicatorsOn{Off, Right, Left, All}
	public float indicatorTimer = 0f;			// Used timer for indicator on / off sequence.

	// Damage.
	public bool useDamage = true;													// Use Damage.
	struct originalMeshVerts{public Vector3[] meshVerts;}				// Struct for Original Mesh Verticies positions.
	private originalMeshVerts[] originalMeshData;							// Array for struct above.
	public MeshFilter[] deformableMeshFilters;								// Deformable Meshes.
	public LayerMask damageFilter = -1;											// LayerMask filter for not taking any damage.
	public float randomizeVertices = 1f;											// Randomize Verticies on Collisions for more complex deforms.
	public float damageRadius = .5f;												// Verticies in this radius will be effected on collisions.
	private float minimumVertDistanceForDamagedMesh = .002f;		// Comparing Original Vertex Positions Between Last Vertex Positions To Decide Mesh Is Repaired Or Not.
	public bool repairNow = false;													// Repair Now.
	public bool repaired = true;														// Returns true if vehicle is repaired.

	public float maximumDamage = .5f;				// Maximum Vert Distance For Limiting Damage. 0 Value Will Disable The Limit.
	private float minimumCollisionForce = 5f;		// Minimum collision force.
	public float damageMultiplier = 1f;				// Damage multiplier.
	
	public GameObject contactSparkle{get{return RCCSettings.contactParticles;}}		// Contact Particles for collisions. It must be Particle System.
	public int maximumContactSparkle = 5;																	//	Contact Particles will be ready to use for collisions in pool. 
	private List<ParticleSystem> contactSparkeList = new List<ParticleSystem>();			// Array for Contact Particles.

	// Used for Angular and Linear Steering Helper.
	private Vector3 localVector;
	private Quaternion rot = Quaternion.identity;
	private float oldRotation;
	public Transform velocityDirection;
	public Transform steeringDirection;
	public float velocityAngle;
	private float angle;
	private float angularVelo;

	// Driving Assistances.
	public bool ABS = true;
	public bool TCS = true;
	public bool ESP = true;
	public bool steeringHelper = true;
	public bool tractionHelper = true;
	public bool angularDragHelper = false;

	// Driving Assistance thresholds.
	[Range(.05f, .5f)]public float ABSThreshold = .35f;
	[Range(.05f, .5f)]public float TCSThreshold = .5f;
	[Range(.05f, 1f)]public float TCSStrength = 1f;
	[Range(.05f, .5f)]public float ESPThreshold = .25f;
	[Range(.05f, 1f)]public float ESPStrength = .5f;
	[Range(0f, 1f)] public float steerHelperLinearVelStrength = .1f;
	[Range(0f, 1f)] public float steerHelperAngularVelStrength = .1f;
	[Range(0f, 1f)] public float tractionHelperStrength = .1f;
	[Range(0f, 1f)] public float angularDragHelperStrength = .1f;

	// Is Driving Assistance is in action now?
	public bool ABSAct = false;
	public bool TCSAct = false;
	public bool ESPAct = false;

	// ESP Bools.
	public bool underSteering = false;
	public bool overSteering = false;

	// Drift Variables.
	internal bool driftingNow = false;										// Currently drifting?
	internal float driftAngle = 0f;											// If we do, what's the drift angle?
	public bool applyCounterSteering = true;							// Applies counter steering when vehicle is drifting. It helps to keep the control fine of the vehicle.
	[Range(0f, 1f)]public float counterSteeringFactor = .5f;	// Counter steering multiplier.

	// Used For ESP.
	public float frontSlip = 0f;
	public float rearSlip = 0f;

	// Turbo and NOS.
	public float turboBoost = 0f;
	public float NoS = 100f;
	private float NoSConsumption = 25f;
	private float NoSRegenerateTime = 10f;

	public bool useNOS = false;
	public bool useTurbo = false;

	public RCC_TruckTrailer attachedTrailer;

	[System.Serializable]
	public class ConfigureVehicleSubsteps{

		public float speedThreshold = 10f;
		public int stepsBelowThreshold = 5;
		public int stepsAboveThreshold = 5;

	}

	public ConfigureVehicleSubsteps configureVehicleSubsteps = new ConfigureVehicleSubsteps();

	// EVENTS

	public delegate void onRCCPlayerSpawned(RCC_CarControllerV3 RCC);
	public static event onRCCPlayerSpawned OnRCCPlayerSpawned;

	public delegate void onRCCPlayerDestroyed(RCC_CarControllerV3 RCC);
	public static event onRCCPlayerDestroyed OnRCCPlayerDestroyed;

	public delegate void onRCCPlayerCollision(RCC_CarControllerV3 RCC, Collision collision);
	public static event onRCCPlayerCollision OnRCCPlayerCollision;

	void Awake (){

		// Overriding Fixed TimeStep.
		if(RCCSettings.overrideFixedTimeStep)
			Time.fixedDeltaTime = RCCSettings.fixedTimeStep;

		// Getting Rigidbody and settings.
		rigid = GetComponent<Rigidbody>();
		rigid.maxAngularVelocity = RCCSettings.maxAngularVelocity;
		gearShiftingThreshold = Mathf.Clamp (gearShiftingThreshold, .25f, .8f);

		// You can configurate wheels for best behavior, but Unity doesn't have docs about it.
		allWheelColliders = GetComponentsInChildren<RCC_WheelCollider>();

		GetComponentInChildren<WheelCollider>().ConfigureVehicleSubsteps(configureVehicleSubsteps.speedThreshold, configureVehicleSubsteps.stepsBelowThreshold, configureVehicleSubsteps.stepsAboveThreshold);

		FrontLeftWheelCollider.wheelModel = FrontLeftWheelTransform;
		FrontRightWheelCollider.wheelModel = FrontRightWheelTransform;
		RearLeftWheelCollider.wheelModel = RearLeftWheelTransform;
		RearRightWheelCollider.wheelModel = RearRightWheelTransform;

		for (int i = 0; i < ExtraRearWheelsCollider.Length; i++) {
			ExtraRearWheelsCollider[i].wheelModel = ExtraRearWheelsTransform[i];
		}

		// Default Steer Angle. Using it for lerping current steer angle between default steer angle and high speed steer angle.
		orgSteerAngle = steerAngle;

		// Collecting all contact particles in same gameobject.
		allContactParticles = new GameObject("All Contact Particles");
		allContactParticles.transform.SetParent(transform, false);

		// Creating torque curves for each gear. Based on speed.
		CreateGearCurves();

		// Creating and initializing all audio sources for this vehicle.
		CreateAudios();

		// Should we use the damage?
		if(useDamage)
			InitDamage();

		CheckBehavior ();

		// Starting the engine.
		if (runEngineAtAwake || externalController) {

			engineRunning = true;
			fuelInput = 1f;

		}

		// Chassis must have the script. Also RCC_Editor script is checking the script while you are on Editor.
		if (chassis) {
			
			if (!chassis.GetComponent<RCC_Chassis> ())
				chassis.AddComponent<RCC_Chassis> ();
			
		}

	}

	void OnEnable(){

		StartCoroutine (RCCPlayerSpawned());

		RCC_SceneManager.OnBehaviorChanged += CheckBehavior;

	}

	/// <summary>
	/// Firing an event when each RCC car spawned / enabled. This event has been listening by RCC_MobileButtons.cs, RCC_DashboardInputs.cs.
	/// </summary>
	/// <returns>The player spawned.</returns>
	private IEnumerator RCCPlayerSpawned(){

		yield return new WaitForEndOfFrame ();

		// Firing an event when each RCC car spawned / enabled. This event has been listening by RCC_MobileButtons.cs, RCC_DashboardInputs.cs.
		if (!externalController) {

			if (OnRCCPlayerSpawned != null)
				OnRCCPlayerSpawned (this);

		}

	}
		
	/// <summary>
	/// Creates the wheel colliders.
	/// </summary>
	public void CreateWheelColliders (){

		// Creating a list for all wheel models.
		List <Transform> allWheelModels = new List<Transform>();
		allWheelModels.Add(FrontLeftWheelTransform); allWheelModels.Add(FrontRightWheelTransform); allWheelModels.Add(RearLeftWheelTransform); allWheelModels.Add(RearRightWheelTransform);

		// If we have additional rear wheels, add them too.
		if (ExtraRearWheelsTransform.Length > 0 && ExtraRearWheelsTransform [0]) {
			
			foreach (Transform t in ExtraRearWheelsTransform)
				allWheelModels.Add (t);
			
		}

		// If we don't have any wheelmodels, throw an error.
		if(allWheelModels != null && allWheelModels[0] == null){
			
			Debug.LogError("You haven't choosen your Wheel Models. Please select all of your Wheel Models before creating Wheel Colliders. Script needs to know their sizes and positions, aye?");
			return;

		}

		// Holding default rotation.
		Quaternion currentRotation = transform.rotation;

		// Resetting rotation.
		transform.rotation = Quaternion.identity;

		// Creating a new gameobject called Wheel Colliders for all Wheel Colliders, and parenting it to this gameobject.
		GameObject WheelColliders = new GameObject("Wheel Colliders");
		WheelColliders.transform.SetParent(transform, false);
		WheelColliders.transform.localRotation = Quaternion.identity;
		WheelColliders.transform.localPosition = Vector3.zero;
		WheelColliders.transform.localScale = Vector3.one;

		// Creating WheelColliders.
		foreach(Transform wheel in allWheelModels){
			
			GameObject wheelcollider = new GameObject(wheel.transform.name); 
			
			wheelcollider.transform.position = RCC_GetBounds.GetBoundsCenter (wheel.transform);
			wheelcollider.transform.rotation = transform.rotation;
			wheelcollider.transform.name = wheel.transform.name;
			wheelcollider.transform.SetParent(WheelColliders.transform);
			wheelcollider.transform.localScale = Vector3.one;
			wheelcollider.AddComponent<WheelCollider>();

			Bounds biggestBound = new Bounds();
			Renderer[] renderers = wheel.GetComponentsInChildren<Renderer>();

			foreach (Renderer render in renderers) {
				if (render != GetComponent<Renderer>()){
					if(render.bounds.size.z > biggestBound.size.z)
						biggestBound = render.bounds;
				}
			}

			wheelcollider.GetComponent<WheelCollider>().radius = (biggestBound.extents.y) / transform.localScale.y;
			wheelcollider.AddComponent<RCC_WheelCollider>();
			JointSpring spring = wheelcollider.GetComponent<WheelCollider>().suspensionSpring;

			spring.spring = 40000f;
			spring.damper = 1500f;
			spring.targetPosition = .5f;

			wheelcollider.GetComponent<WheelCollider>().suspensionSpring = spring;
			wheelcollider.GetComponent<WheelCollider>().suspensionDistance = .2f;
			wheelcollider.GetComponent<WheelCollider>().forceAppPointDistance = 0f;
			wheelcollider.GetComponent<WheelCollider>().mass = 40f;
			wheelcollider.GetComponent<WheelCollider>().wheelDampingRate = 1f;

			WheelFrictionCurve sidewaysFriction;
			WheelFrictionCurve forwardFriction;
			
			sidewaysFriction = wheelcollider.GetComponent<WheelCollider>().sidewaysFriction;
			forwardFriction = wheelcollider.GetComponent<WheelCollider>().forwardFriction;

			forwardFriction.extremumSlip = .3f;
			forwardFriction.extremumValue = 1;
			forwardFriction.asymptoteSlip = .8f;
			forwardFriction.asymptoteValue = .6f;
			forwardFriction.stiffness = 1.5f;

			sidewaysFriction.extremumSlip = .3f;
			sidewaysFriction.extremumValue = 1;
			sidewaysFriction.asymptoteSlip = .5f;
			sidewaysFriction.asymptoteValue = .8f;
			sidewaysFriction.stiffness = 1.5f;

			wheelcollider.GetComponent<WheelCollider>().sidewaysFriction = sidewaysFriction;
			wheelcollider.GetComponent<WheelCollider>().forwardFriction = forwardFriction;

		}
		
		RCC_WheelCollider[] allWheelColliders = new RCC_WheelCollider[allWheelModels.Count];
		allWheelColliders = GetComponentsInChildren<RCC_WheelCollider>();
		
		FrontLeftWheelCollider = allWheelColliders[0];
		FrontRightWheelCollider = allWheelColliders[1];
		RearLeftWheelCollider = allWheelColliders[2];
		RearRightWheelCollider = allWheelColliders[3];

		ExtraRearWheelsCollider = new RCC_WheelCollider[ExtraRearWheelsTransform.Length];

		for (int i = 0; i < ExtraRearWheelsTransform.Length; i++) {
			ExtraRearWheelsCollider [i] = allWheelColliders [i + 4];
		}

		transform.rotation = currentRotation;
		
	}

	/// <summary>
	/// Creates the audios.
	/// </summary>
	private void CreateAudios (){

		switch (audioType) {

		case AudioType.OneSource:

			engineSoundHigh = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High AudioSource", 5, 50, 0, engineClipHigh, true, true, false);

			if (autoCreateEngineOffSounds) {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHigh, true, true, false);

				RCC_CreateAudioSource.NewLowPassFilter (engineSoundHighOff, 3000f);

			} else {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHighOff, true, true, false);

			}

			break;

		case AudioType.TwoSource:

			engineSoundHigh = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High AudioSource", 5, 50, 0, engineClipHigh, true, true, false);
			engineSoundLow = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low AudioSource", 5, 25, 0, engineClipLow, true, true, false);

			if (autoCreateEngineOffSounds) {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHigh, true, true, false);
				engineSoundLowOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low Off AudioSource", 5, 25, 0, engineClipLow, true, true, false);

				RCC_CreateAudioSource.NewLowPassFilter (engineSoundHighOff, 3000f);
				RCC_CreateAudioSource.NewLowPassFilter (engineSoundLowOff, 3000f);

			} else {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHighOff, true, true, false);
				engineSoundLowOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low Off AudioSource", 5, 25, 0, engineClipLowOff, true, true, false);

			}

			break;

		case AudioType.ThreeSource:

			engineSoundHigh = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High AudioSource", 5, 50, 0, engineClipHigh, true, true, false);
			engineSoundMed = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Medium AudioSource", 5, 50, 0, engineClipMed, true, true, false);
			engineSoundLow = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low AudioSource", 5, 25, 0, engineClipLow, true, true, false);

			if (autoCreateEngineOffSounds) {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHigh, true, true, false);
				engineSoundMedOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Medium Off AudioSource", 5, 50, 0, engineClipMed, true, true, false);
				engineSoundLowOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low Off AudioSource", 5, 25, 0, engineClipLow, true, true, false);

				if(engineSoundHighOff)
					RCC_CreateAudioSource.NewLowPassFilter (engineSoundHighOff, 3000f);
				if(engineSoundMedOff)
					RCC_CreateAudioSource.NewLowPassFilter (engineSoundMedOff, 3000f);
				if(engineSoundLowOff)
					RCC_CreateAudioSource.NewLowPassFilter (engineSoundLowOff, 3000f);

			} else {

				engineSoundHighOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound High Off AudioSource", 5, 50, 0, engineClipHighOff, true, true, false);
				engineSoundMedOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Medium Off AudioSource", 5, 50, 0, engineClipMedOff, true, true, false);
				engineSoundLowOff = RCC_CreateAudioSource.NewAudioSource (gameObject, "Engine Sound Low Off AudioSource", 5, 25, 0, engineClipLowOff, true, true, false);

			}

			break;

		}

		engineSoundIdle = RCC_CreateAudioSource.NewAudioSource(gameObject, "Engine Sound Idle AudioSource", 5, 25, 0, engineClipIdle, true, true, false);
		reversingSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Reverse Sound AudioSource", 1, 10, 0, reversingClip, true, false, false);
		windSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Wind Sound AudioSource", 1, 10, 0, windClip, true, true, false);
		brakeSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Brake Sound AudioSource", 1, 10, 0, brakeClip, true, true, false);

		if(useNOS)
			NOSSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "NOS Sound AudioSource", 5, 10, 1f, NOSClip, true, false, false);
		if(useNOS || useTurbo)
			blowSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "NOS Blow", 1, 10, 1, null, false, false, false);
		if(useTurbo){
			turboSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Turbo Sound AudioSource", .1f, .5f, 0, turboClip, true, true, false);
			RCC_CreateAudioSource.NewHighPassFilter(turboSound, 10000f, 10);
		}
		
	}

	/// <summary>
	/// Overrides the behavior.
	/// </summary>
	private void CheckBehavior (){

		if (RCCSettings.selectedBehaviorType == null)
			return;

		RCC_Settings.BehaviorType currentBehaviorType = RCCSettings.selectedBehaviorType;

		steeringHelper = currentBehaviorType.steeringHelper;
		tractionHelper = currentBehaviorType.tractionHelper;
		ABS = currentBehaviorType.ABS;
		ESP = currentBehaviorType.ESP;
		TCS = currentBehaviorType.TCS;

		highspeedsteerAngle = Mathf.Clamp(highspeedsteerAngle, currentBehaviorType.highSpeedSteerAngleMinimum, currentBehaviorType.highSpeedSteerAngleMaximum);
		highspeedsteerAngleAtspeed = Mathf.Clamp(highspeedsteerAngleAtspeed, currentBehaviorType.highSpeedSteerAngleAtspeedMinimum, currentBehaviorType.highSpeedSteerAngleAtspeedMaximum);

		steerHelperAngularVelStrength = Mathf.Clamp(steerHelperAngularVelStrength, currentBehaviorType.steerHelperAngularVelStrengthMinimum, currentBehaviorType.steerHelperAngularVelStrengthMaximum);
		steerHelperLinearVelStrength = Mathf.Clamp(steerHelperLinearVelStrength, currentBehaviorType.steerHelperLinearVelStrengthMinimum, currentBehaviorType.steerHelperLinearVelStrengthMaximum);

		tractionHelperStrength = Mathf.Clamp(tractionHelperStrength, currentBehaviorType.tractionHelperStrengthMinimum, currentBehaviorType.tractionHelperStrengthMaximum);
		antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, currentBehaviorType.antiRollFrontHorizontalMinimum, Mathf.Infinity);
		antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, currentBehaviorType.antiRollRearHorizontalMinimum, Mathf.Infinity);

		gearShiftingDelay = Mathf.Clamp(gearShiftingDelay, 0f, currentBehaviorType.gearShiftingDelayMaximum);
		rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, currentBehaviorType.angularDrag, 1f);

		//		// Proper settings for selected behavior type.
		//		switch(RCCSettings.behaviorType){
		//
		//		case RCC_Settings.BehaviorType.SemiArcade:
		//			steeringHelper = true;
		//			tractionHelper = true;
		//			ABS = false;
		//			ESP = false;
		//			TCS = false;
		//			steerHelperLinearVelStrength = Mathf.Clamp(steerHelperLinearVelStrength, .5f, 1f);
		//			steerHelperAngularVelStrength = Mathf.Clamp(steerHelperAngularVelStrength, 1f, 1f);
		//			tractionHelperStrength = Mathf.Clamp(tractionHelperStrength, .25f, 1f);
		//			antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, 10000f, Mathf.Infinity);
		//			antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, 10000f, Mathf.Infinity);
		//			gearShiftingDelay = Mathf.Clamp(gearShiftingDelay, 0f, .1f);
		//			rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, .5f, 1f);
		//			break;
		//
		//		case RCC_Settings.BehaviorType.Drift:
		//			steeringHelper = true;
		//			tractionHelper = true;
		//			ABS = false;
		//			ESP = false;
		//			TCS = false;
		//			highspeedsteerAngle = Mathf.Clamp(highspeedsteerAngle, 40f, 60f);
		//			highspeedsteerAngleAtspeed = Mathf.Clamp(highspeedsteerAngleAtspeed, 100f, maxspeed);
		//			steerHelperAngularVelStrength = Mathf.Clamp(steerHelperAngularVelStrength, .025f, .5f);
		//			steerHelperLinearVelStrength = 0f;
		//			tractionHelperStrength = Mathf.Clamp(tractionHelperStrength, .1f, .5f);
		//			antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, 1000f, Mathf.Infinity);
		//			antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, 1000f, Mathf.Infinity);
		//			gearShiftingDelay = Mathf.Clamp(gearShiftingDelay, 0f, .15f);
		//			rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, .5f, 1f);
		//			break;
		//
		//		case RCC_Settings.BehaviorType.Fun:
		//			steeringHelper = true;
		//			tractionHelper = true;
		//			ABS = false;
		//			ESP = false;
		//			TCS = false;
		//			steerHelperLinearVelStrength = 1f;
		//			steerHelperAngularVelStrength = 1f;
		//			highspeedsteerAngle = Mathf.Clamp(highspeedsteerAngle, 30f, 50f);
		//			highspeedsteerAngleAtspeed = Mathf.Clamp(highspeedsteerAngleAtspeed, 100f, maxspeed);
		//			antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, 20000f, Mathf.Infinity);
		//			antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, 20000f, Mathf.Infinity);
		//			gearShiftingDelay = Mathf.Clamp(gearShiftingDelay, 0f, .1f);
		//			rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, .5f, 1f);
		//			break;
		//
		//		case RCC_Settings.BehaviorType.Racing:
		//			steeringHelper = true;
		//			tractionHelper = true;
		//			steerHelperLinearVelStrength = Mathf.Clamp(steerHelperLinearVelStrength, .25f, 1f);
		//			steerHelperAngularVelStrength = Mathf.Clamp(steerHelperAngularVelStrength, .25f, 1f);
		//			tractionHelperStrength = Mathf.Clamp(tractionHelperStrength, .25f, 1f);
		//			antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, 10000f, Mathf.Infinity);
		//			antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, 10000f, Mathf.Infinity);
		//			rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, .5f, 1f);
		//			break;
		//
		//		case RCC_Settings.BehaviorType.Simulator:
		//			antiRollFrontHorizontal = Mathf.Clamp(antiRollFrontHorizontal, 1000f, Mathf.Infinity);
		//			antiRollRearHorizontal = Mathf.Clamp(antiRollRearHorizontal, 1000f, Mathf.Infinity);
		//			rigid.angularDrag = Mathf.Clamp(rigid.angularDrag, .5f, 1f);
		//			break;
		//
		//		}

	}
		
	/// <summary>
	/// Creates the gear curves.
	/// </summary>
	public void CreateGearCurves (){

		gears = new Gear[totalGears];

		float[] gearRatio = new float[gears.Length];
		int[] maxSpeedForGear = new int[gears.Length];

		if (gears.Length == 3) {

			gearRatio = new float[]{2.0f, 1.5f, 1.0f};

		}

		if (gears.Length == 4) {

			gearRatio = new float[]{2.86f, 1.62f, 1.0f, .72f};

		}

		if (gears.Length == 5) {

			gearRatio = new float[] {4.23f, 2.52f, 1.66f, 1.22f, 1.0f,};

		}

		if (gears.Length == 6) {

			gearRatio = new float[]{4.35f, 2.5f, 1.66f, 1.23f, 1.0f, .85f};

		}

		if (gears.Length == 7) {

			gearRatio = new float[]{4.5f, 2.5f, 1.66f, 1.23f, 1.0f, .9f, .8f};

		}

		if (gears.Length == 8) {

			gearRatio = new float[]{4.6f, 2.5f, 1.86f, 1.43f, 1.23f, 1.05f, .9f, .72f};

		}

		for (int i = 0; i < maxSpeedForGear.Length; i++) {

			maxSpeedForGear [i] = new int ();
			maxSpeedForGear[i] = (int)((maxspeed / gears.Length) * (i+1));

		}

		for (int i = 0; i < gears.Length; i++) {

			gears [i] = new Gear ();
			gears [i].SetGear (gearRatio [i], maxSpeedForGear [i], (int)(Mathf.Lerp (0, maxspeed * Mathf.Lerp(0f, 1f, gearShiftingThreshold), ((float)(i + 1) / (float)(gears.Length)))));

		}

		if (autoGenerateGearCurves) {

			engineTorqueCurve = new AnimationCurve[gears.Length];

			currentGear = 0;

			for (int i = 0; i < engineTorqueCurve.Length; i++)
				engineTorqueCurve [i] = new AnimationCurve (new Keyframe (0, 1));

			for (int i = 0; i < gears.Length; i++) {

				if (i != 0) {

					engineTorqueCurve [i].MoveKey (0, new Keyframe (0, Mathf.Lerp (1f, .05f, (float)(i + 1) / (float)gears.Length)));
					engineTorqueCurve [i].AddKey (gears[i].targetSpeedForNextGear / 3f, gears[i].maxRatio / 1.1f);
//					engineTorqueCurve [i].AddKey (gears[i-1].targetSpeedForNextGear, gears[i].maxRatio);
//					engineTorqueCurve [i].AddKey (gears[i-1].targetSpeedForNextGear + ((gears[i].targetSpeedForNextGear - gears[i-1].targetSpeedForNextGear) / 2f), gears[i].maxRatio);
					engineTorqueCurve [i].AddKey (gears[i].targetSpeedForNextGear, gears[i].maxRatio);
					engineTorqueCurve [i].AddKey (gears[i].maxSpeed, Mathf.Lerp (1f, .05f, (float)(i + 1) / (float)gears.Length));
					engineTorqueCurve [i].postWrapMode = WrapMode.Clamp;
					engineTorqueCurve [i].preWrapMode = WrapMode.Clamp;
					engineTorqueCurve [i].SmoothTangents (1, -.5f);
					engineTorqueCurve [i].SmoothTangents (2, .75f);

				} else {

					engineTorqueCurve [i].MoveKey (0, new Keyframe (0, gears[0].maxRatio));
					engineTorqueCurve [i].AddKey (gears[0].targetSpeedForNextGear, gears[0].maxRatio / 1.25f);
					engineTorqueCurve [i].AddKey (gears[0].maxSpeed, Mathf.Lerp (1f, .05f, (float)(1) / (float)gears.Length));
					engineTorqueCurve [i].postWrapMode = WrapMode.Clamp;
					engineTorqueCurve [i].preWrapMode = WrapMode.Clamp;
//					engineTorqueCurve [i].SmoothTangents (1, -.5f);
					engineTorqueCurve [i].SmoothTangents (1, .5f);
//					engineTorqueCurve [i].SmoothTangents (2, .75f);

				}

				orgMaxSpeed = maxspeed;
				orgGearShiftingThreshold = gearShiftingThreshold;

			}

		}

		for (int i = 0; i < engineTorqueCurve.Length; i++)
			gears [i].torqueCurve = engineTorqueCurve [i];

	}

	/// <summary>
	/// Collecting all meshes for damage.
	/// </summary>
	private void InitDamage (){

		if (deformableMeshFilters.Length == 0){

			MeshFilter[] allMeshFilters = GetComponentsInChildren<MeshFilter>();
			List <MeshFilter> properMeshFilters = new List<MeshFilter>();

			foreach(MeshFilter mf in allMeshFilters){

				if(!mf.transform.IsChildOf(FrontLeftWheelTransform) && !mf.transform.IsChildOf(FrontRightWheelTransform) && !mf.transform.IsChildOf(RearLeftWheelTransform) && !mf.transform.IsChildOf(RearRightWheelTransform))
					properMeshFilters.Add(mf);

			}

			deformableMeshFilters = properMeshFilters.ToArray();

		}

		LoadOriginalMeshData();

		// Particle System used for collision effects. Creating it at start. We will use this when we collide something.
		if(contactSparkle){

			for(int i = 0; i < maximumContactSparkle; i++){
				GameObject sparks = (GameObject)Instantiate(contactSparkle, transform.position, Quaternion.identity) as GameObject;
				sparks.transform.SetParent(allContactParticles.transform);
				contactSparkeList.Add(sparks.GetComponent<ParticleSystem>());
				ParticleSystem.EmissionModule em = sparks.GetComponent<ParticleSystem>().emission;
				em.enabled = false;
			}

		}

	}
		
	/// <summary>
	/// Kills  or start engine.
	/// </summary>
	public void KillOrStartEngine (){
		
		if(engineRunning)
			KillEngine ();
		else
			StartEngine();

	}

	/// <summary>
	/// Starts the engine.
	/// </summary>
	public void StartEngine (){

		if(!engineRunning)
			StartCoroutine (StartEngineDelayed());

	}

	/// <summary>
	/// Starts the engine.
	/// </summary>
	/// <param name="instantStart">If set to <c>true</c> instant start.</param>
	public void StartEngine (bool instantStart){

		if (instantStart) {
			
			fuelInput = 1f;
			engineRunning = true;

		} else {

			StartCoroutine (StartEngineDelayed());

		}

	}

	/// <summary>
	/// Starts the engine delayed.
	/// </summary>
	/// <returns>The engine delayed.</returns>
	public IEnumerator StartEngineDelayed (){

		if (!engineRunning) {

			engineStartSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Engine Start AudioSource", 1, 10, 1, engineStartClip, false, true, true);

			if(engineStartSound.isPlaying)
				engineStartSound.Play();
			
			yield return new WaitForSeconds(1f);

			engineRunning = true;
			fuelInput = 1f;

		}

		yield return new WaitForSeconds(1f);

	}

	/// <summary>
	/// Kills the engine.
	/// </summary>
	public void KillEngine (){

		fuelInput = 0f;
		engineRunning = false;

	}

	/// <summary>
	/// Default mesh vertices positions. Used for repairing the vehicle body.
	/// </summary>
	private void LoadOriginalMeshData(){

		originalMeshData = new originalMeshVerts[deformableMeshFilters.Length];

		for (int i = 0; i < deformableMeshFilters.Length; i++)
			originalMeshData[i].meshVerts = deformableMeshFilters[i].mesh.vertices;

	}

	/// <summary>
	/// Moving deformed vertices to their original positions while repairing.
	/// </summary>
	public void Repair(){

		if (!repaired && repairNow){
			
			int k;
			repaired = true;

			for(k = 0; k < deformableMeshFilters.Length; k++){

				Vector3[] vertices = deformableMeshFilters[k].mesh.vertices;

				if(originalMeshData == null)
					LoadOriginalMeshData();

				for (int i = 0; i < vertices.Length; i++){

					vertices[i] += (originalMeshData[k].meshVerts[i] - vertices[i]) * (Time.deltaTime * 2f);
					if((originalMeshData[k].meshVerts[i] - vertices[i]).magnitude >= minimumVertDistanceForDamagedMesh)
						repaired = false;

				}

				deformableMeshFilters[k].mesh.vertices = vertices;
				deformableMeshFilters[k].mesh.RecalculateNormals();
				deformableMeshFilters[k].mesh.RecalculateBounds();

			}
			
			if(repaired)
				repairNow = false;
			
		}

	}

	/// <summary>
	/// Actual mesh deformation on collision.	/// </summary>
	/// <param name="mesh">Mesh.</param>
	/// <param name="originalMesh">Original mesh.</param>
	/// <param name="collision">Collision.</param>
	/// <param name="cos">Cos.</param>
	/// <param name="meshTransform">Mesh transform.</param>
	/// <param name="rot">Rot.</param>
	private void DeformMesh(Mesh mesh, Vector3[] originalMesh, Collision collision, float cos, Transform meshTransform, Quaternion rot){
		
		Vector3[] vertices = mesh.vertices;
		
		foreach (ContactPoint contact in collision.contacts){
			
			Vector3 point = meshTransform.InverseTransformPoint(contact.point);
			 
			for (int i = 0; i < vertices.Length; i++){

				if ((point - vertices[i]).magnitude < damageRadius){
					vertices[i] += rot * ((localVector * (damageRadius - (point - vertices[i]).magnitude) / damageRadius) * cos + (new Vector3(Mathf.Sin(vertices[i].y * 1000), Mathf.Sin(vertices[i].z * 1000), Mathf.Sin(vertices[i].x * 100)).normalized * (randomizeVertices / 500f)));
					if (maximumDamage > 0 && ((vertices[i] - originalMesh[i]).magnitude) > maximumDamage){
						vertices[i] = originalMesh[i] + (vertices[i] - originalMesh[i]).normalized * (maximumDamage);
					}
				}
					
			}
			
		}
		
		mesh.vertices = vertices;
		mesh.RecalculateNormals();
		mesh.RecalculateBounds();
		;
		
	}

	/// <summary>
	/// Enabling contact particles on collision.
	/// </summary>
	/// <param name="contactPoint">Contact point.</param>
	private void CollisionParticles(Vector3 contactPoint){
		
		for(int i = 0; i < contactSparkeList.Count; i++){
			if(contactSparkeList[i].isPlaying)
				return;
			contactSparkeList[i].transform.position = contactPoint;
			ParticleSystem.EmissionModule em = contactSparkeList[i].emission;
			em.enabled = true;
			contactSparkeList[i].Play();
		}
		
	}

	/// <summary>
	/// Other visuals.
	/// </summary>
	private void OtherVisuals(){

		//Driver SteeringWheel Transform.
		if (SteeringWheel) {

			if (orgSteeringWheelRot.eulerAngles == Vector3.zero)
				orgSteeringWheelRot = SteeringWheel.transform.localRotation;
			
			switch (steeringWheelRotateAround) {

			case SteeringWheelRotateAround.XAxis:
				SteeringWheel.transform.localRotation = orgSteeringWheelRot * Quaternion.AngleAxis(((FrontLeftWheelCollider.wheelCollider.steerAngle) * -steeringWheelAngleMultiplier), Vector3.right);
				break;

			case SteeringWheelRotateAround.YAxis:
				SteeringWheel.transform.localRotation = orgSteeringWheelRot * Quaternion.AngleAxis(((FrontLeftWheelCollider.wheelCollider.steerAngle) * -steeringWheelAngleMultiplier), Vector3.up);
				break;

			case SteeringWheelRotateAround.ZAxis:
				SteeringWheel.transform.localRotation = orgSteeringWheelRot * Quaternion.AngleAxis(((FrontLeftWheelCollider.wheelCollider.steerAngle) * -steeringWheelAngleMultiplier), Vector3.forward);
				break;

			}

		}

	}
	
	void Update (){
		
		if(canControl){
			
			if(!externalController)
				Inputs();
			
		}else if(!externalController){
			
			_gasInput = 0f;
			brakeInput = 0f;
			boostInput = 0f;
			handbrakeInput = 1f;

		}
			
		Audio();
		ResetCar();

		if(useDamage)
			Repair();

		OtherVisuals ();

		indicatorTimer += Time.deltaTime;

		if (_gasInput >= .1f)
			launched += _gasInput * Time.deltaTime;
		else
			launched -= Time.deltaTime;
		
		launched = Mathf.Clamp01 (launched);
		
	}

	/// <summary>
	/// Inputs this instance.
	/// </summary>
	private void Inputs(){
		
		switch(RCCSettings.controllerType){

		case RCC_Settings.ControllerType.Keyboard:
			
			gasInput = Input.GetAxis(RCCSettings.verticalInput);
			brakeInput = Mathf.Clamp01(-Input.GetAxis(RCCSettings.verticalInput));
			handbrakeInput = Input.GetKey(RCCSettings.handbrakeKB) ? 1f : 0f;
			steerInput = Input.GetAxis(RCCSettings.horizontalInput);
			boostInput = Input.GetKey(RCCSettings.boostKB) ? 1f : 0f;

			if(Input.GetKeyDown(RCCSettings.lowBeamHeadlightsKB)){
				lowBeamHeadLightsOn = !lowBeamHeadLightsOn;
			}

			if(Input.GetKeyDown(RCCSettings.highBeamHeadlightsKB)){
				highBeamHeadLightsOn = true;
			}else if(Input.GetKeyUp(RCCSettings.highBeamHeadlightsKB)){
				highBeamHeadLightsOn = false;
			}

			if(Input.GetKeyDown(RCCSettings.startEngineKB))
				KillOrStartEngine();

			if(Input.GetKeyDown(RCCSettings.trailerAttachDetach))
				DetachTrailer();

			if(Input.GetKeyDown(RCCSettings.rightIndicatorKB)){
				if(indicatorsOn != IndicatorsOn.Right)
					indicatorsOn = IndicatorsOn.Right;
				else
					indicatorsOn = IndicatorsOn.Off;
			}

			if(Input.GetKeyDown(RCCSettings.leftIndicatorKB)){
				if(indicatorsOn != IndicatorsOn.Left)
					indicatorsOn = IndicatorsOn.Left;
				else
					indicatorsOn = IndicatorsOn.Off;
			}

			if(Input.GetKeyDown(RCCSettings.hazardIndicatorKB)){
				if(indicatorsOn != IndicatorsOn.All){
					indicatorsOn = IndicatorsOn.Off;
					indicatorsOn = IndicatorsOn.All;
				}else{
					indicatorsOn = IndicatorsOn.Off;
				}
			}

			if (Input.GetKeyDown (RCCSettings.NGear))
				NGear = true;

			if (Input.GetKeyUp (RCCSettings.NGear))
				NGear = false;

			if(!automaticGear){

				if (Input.GetKeyDown (RCCSettings.shiftGearUp))
					GearShiftUp ();

				if(Input.GetKeyDown(RCCSettings.shiftGearDown))
					GearShiftDown();	

			}

			break;

		case RCC_Settings.ControllerType.XBox360One:

			gasInput = Input.GetAxis (RCCSettings.Xbox_triggerRightInput);
			brakeInput = Input.GetAxis (RCCSettings.Xbox_triggerLeftInput);
			steerInput = Input.GetAxis (RCCSettings.Xbox_horizontalInput);

			handbrakeInput = Input.GetButton (RCCSettings.Xbox_handbrakeKB) ? 1f : 0f;
			boostInput = Input.GetButton(RCCSettings.Xbox_boostKB) ? 1f : 0f;

			if (Input.GetButtonDown (RCCSettings.Xbox_lowBeamHeadlightsKB)) {
				lowBeamHeadLightsOn = !lowBeamHeadLightsOn;
			}

			if (Input.GetButtonDown (RCCSettings.Xbox_highBeamHeadlightsKB)) {
				highBeamHeadLightsOn = true;
			} else if (Input.GetButtonUp (RCCSettings.Xbox_highBeamHeadlightsKB)) {
				highBeamHeadLightsOn = false;
			}

			if (Input.GetButtonDown (RCCSettings.Xbox_startEngineKB))
				KillOrStartEngine ();

			if(Input.GetButtonDown(RCCSettings.Xbox_trailerAttachDetach))
				DetachTrailer();
//
			float indicator = Input.GetAxis (RCCSettings.Xbox_indicatorKB);
			float indicatorHazard = Input.GetAxis (RCCSettings.Xbox_hazardIndicatorKB);

			if (indicatorHazard >= .5f) {

				if (indicatorsOn != IndicatorsOn.All)
					indicatorsOn = IndicatorsOn.All;

			}else if (indicator >= .5f) {
				
				if (indicatorsOn != IndicatorsOn.Right)
					indicatorsOn = IndicatorsOn.Right;
				
			} else if (indicator <= -.5f) {
				
				if (indicatorsOn != IndicatorsOn.Left)
					indicatorsOn = IndicatorsOn.Left;

			} else {

				indicatorsOn = IndicatorsOn.Off;

			}

//
//			if (Input.GetKeyDown (RCCSettings.NGear))
//				NGear = true;
//
//			if (Input.GetKeyUp (RCCSettings.NGear))
//				NGear = false;
//
			if (!automaticGear) {

				if (Input.GetButtonDown (RCCSettings.Xbox_shiftGearUp))
					GearShiftUp ();

				if (Input.GetButtonDown (RCCSettings.Xbox_shiftGearDown))
					GearShiftDown ();	

			}
			
			break;

		}

		if (permanentGas)
			gasInput = 1f;

	}
	
	void FixedUpdate (){

		if(rigid.velocity.magnitude < .01f && Mathf.Abs(_steerInput) < .01f && Mathf.Abs(_gasInput) < .01f && Mathf.Abs(rigid.angularVelocity.magnitude) < .01f)
			isSleeping = true;
		else
			isSleeping = false;

		if (orgMaxSpeed != maxspeed || orgGearShiftingThreshold != gearShiftingThreshold)
			CreateGearCurves();

		if (gears == null || gears.Length == 0) {

			print ("Gear can not be 0!");
			gears = new Gear[totalGears];
			CreateGearCurves ();

		}
		
		Engine();
		EngineSounds ();

		if (canControl) {
			
			GearBox ();
			Clutch ();

		}

		AntiRollBars();
		DriftVariables();
		RevLimiter();
		Turbo();
		NOS();

		if(useFuelConsumption)
			Fuel();

		if (useEngineHeat)
			EngineHeat ();

		if(steeringHelper)
			SteerHelper();
		
		if(tractionHelper)
			TractionHelper();

		if (angularDragHelper)
			AngularDragHelper ();

		if(ESP)
			ESPCheck(FrontLeftWheelCollider.wheelCollider.steerAngle);

		if(RCCSettings.selectedBehaviorType != null && RCCSettings.selectedBehaviorType.applyRelativeTorque){
			
			if (RearLeftWheelCollider.wheelCollider.isGrounded)
				rigid.AddRelativeTorque (Vector3.up * (((steerInput * _gasInput) * direction)) / 1f, ForceMode.Acceleration);
			 
		}
			
		rigid.centerOfMass = transform.InverseTransformPoint(COM.transform.position);
		rigid.AddRelativeForce (Vector3.down * (speed * downForce), ForceMode.Force);

	}

	/// <summary>
	/// Engine.
	/// </summary>
	private void Engine (){
		
		//Speed.
		speed = rigid.velocity.magnitude * 3.6f;

		//Steer Limit.
		steerAngle = Mathf.Lerp(orgSteerAngle, highspeedsteerAngle, (speed / highspeedsteerAngleAtspeed));

		float wheelRPM = wheelTypeChoise == WheelType.FWD ? (FrontLeftWheelCollider.wheelRPMToSpeed + FrontRightWheelCollider.wheelRPMToSpeed) : (RearLeftWheelCollider.wheelRPMToSpeed + RearRightWheelCollider.wheelRPMToSpeed);
		
		rawEngineRPM = Mathf.Clamp(Mathf.MoveTowards(rawEngineRPM, (maxEngineRPM * 1.1f) * 
			(Mathf.Clamp01(Mathf.Lerp(0f, 1f, (1f - clutchInput) * 
				(((wheelRPM * direction) / 2f) / gears[currentGear].maxSpeed)) + 
				(((_gasInput) * clutchInput) + idleInput)))
		                                             , engineInertia * 100f), 0f, maxEngineRPM * 1.1f);
		
		rawEngineRPM *= _fuelInput;
		engineRPM = Mathf.Lerp(engineRPM, rawEngineRPM, Mathf.Lerp(Time.fixedDeltaTime * 5f, Time.fixedDeltaTime * 50f, rawEngineRPM / maxEngineRPM));
		
		//Auto Reverse Bool.
		if(autoReverse){
			
			canGoReverseNow = true;

		}else{
			
			if(_brakeInput < .5f && speed < 5)
				canGoReverseNow = true;
			else if(_brakeInput > 0 && transform.InverseTransformDirection(rigid.velocity).z > 1f)
				canGoReverseNow = false;
			
		}

	}

	/// <summary>
	/// Audio.
	/// </summary>
	private void Audio(){

		// Improved wind sound with speed limit and better balance
		float windVolumeMultiplier = Mathf.Clamp01(speed / audioBalanceSpeedThreshold);
		windSound.volume = Mathf.Lerp (0f, RCCSettings.maxWindSoundVolume * highSpeedVolumeReduction, windVolumeMultiplier);
		// Stable wind pitch - less random variation at high speeds
		float windPitchVariation = Mathf.Lerp(0.1f, 0.05f, Mathf.Clamp01(speed / 100f)); // Less variation at high speed
		windSound.pitch = Mathf.Lerp(0.95f, 1.05f, UnityEngine.Random.Range(0.5f - windPitchVariation, 0.5f + windPitchVariation));

		if(direction == 1)
			brakeSound.volume = Mathf.Lerp (0f, RCCSettings.maxBrakeSoundVolume, Mathf.Clamp01((FrontLeftWheelCollider.wheelCollider.brakeTorque + FrontRightWheelCollider.wheelCollider.brakeTorque) / (brakeTorque * 2f)) * Mathf.Lerp(0f, 1f, FrontLeftWheelCollider.wheelCollider.rpm / 50f));
		else
			brakeSound.volume = 0f;

	}

	/// <summary>
	/// ESPs the check.
	/// </summary>
	/// <param name="steering">Steering.</param>
	private void ESPCheck(float steering){

		WheelHit frontHit1;
		FrontLeftWheelCollider.wheelCollider.GetGroundHit(out frontHit1);

		WheelHit frontHit2;
		FrontRightWheelCollider.wheelCollider.GetGroundHit(out frontHit2);

		frontSlip = frontHit1.sidewaysSlip + frontHit2.sidewaysSlip;

		WheelHit rearHit1;
		RearLeftWheelCollider.wheelCollider.GetGroundHit(out rearHit1);

		WheelHit rearHit2;
		RearRightWheelCollider.wheelCollider.GetGroundHit(out rearHit2);

		rearSlip = rearHit1.sidewaysSlip + rearHit2.sidewaysSlip;

		if(Mathf.Abs(frontSlip) >= ESPThreshold)
			underSteering = true;
		else
			underSteering = false;

		if(Mathf.Abs(rearSlip) >= ESPThreshold)
			overSteering = true;
		else
			overSteering = false;

		if(overSteering || underSteering)
			ESPAct = true;
		else
			ESPAct = false;
			
	}

	/// <summary>
	/// Engine sounds.
	/// </summary>
	private void EngineSounds(){

		float lowRPM = 0f;
		float medRPM = 0f;
		float highRPM = 0f;

		if(engineRPM < ((maxEngineRPM) / 2f))
			lowRPM = Mathf.Lerp(0f, 1f, engineRPM / ((maxEngineRPM) / 2f));
		else
			lowRPM = Mathf.Lerp(1f, .25f, engineRPM / maxEngineRPM);

		if(engineRPM < ((maxEngineRPM) / 2f))
			medRPM = Mathf.Lerp(-.5f, 1f, engineRPM / ((maxEngineRPM) / 2f));
		else
			medRPM = Mathf.Lerp(1f, .5f, engineRPM / maxEngineRPM);

		highRPM = Mathf.Lerp(-1f, 1f, engineRPM / maxEngineRPM);

		// Apply speed-based volume reduction to prevent audio imbalance at high speeds
		float speedVolumeReduction = Mathf.Lerp(1f, highSpeedVolumeReduction, Mathf.Clamp01(speed / audioBalanceSpeedThreshold));

		lowRPM = Mathf.Clamp01 (lowRPM) * maxEngineSoundVolume * speedVolumeReduction;
		medRPM = Mathf.Clamp01 (medRPM) * maxEngineSoundVolume * speedVolumeReduction;
		highRPM = Mathf.Clamp01 (highRPM) * maxEngineSoundVolume * speedVolumeReduction;

		float volumeLevel = Mathf.Clamp (_gasInput, 0f, 1f);

		// Improved pitch calculation with speed-based limiting
		float speedPitchLimit = Mathf.Lerp(maxEngineSoundPitch, maxPitchAtHighSpeed, Mathf.Clamp01(speed / audioBalanceSpeedThreshold));
		float basePitchLevel = Mathf.Lerp (minEngineSoundPitch, speedPitchLimit, engineRPM / maxEngineRPM);
		float pitchLevel = Mathf.Lerp(basePitchLevel, basePitchLevel * pitchSmoothingFactor, Mathf.Clamp01(speed / 200f)) * (engineRunning ? 1f : 0f);

		switch (audioType) {

		case RCC_CarControllerV3.AudioType.OneSource:

			engineSoundHigh.volume = volumeLevel * maxEngineSoundVolume * speedVolumeReduction;
			engineSoundHigh.pitch = pitchLevel;

			engineSoundHighOff.volume = (1f - volumeLevel) * maxEngineSoundVolume * speedVolumeReduction;
			engineSoundHighOff.pitch = pitchLevel;

			if(engineSoundIdle){

				engineSoundIdle.volume = Mathf.Lerp(engineRunning ? idleEngineSoundVolume : 0f, 0f, engineRPM / maxEngineRPM) * speedVolumeReduction;
				engineSoundIdle.pitch = pitchLevel;

			}

			if(!engineSoundHigh.isPlaying)
				engineSoundHigh.Play();
			if(!engineSoundIdle.isPlaying)
				engineSoundIdle.Play();

			break;

		case RCC_CarControllerV3.AudioType.TwoSource:

			engineSoundHigh.volume = highRPM * volumeLevel;
			engineSoundHigh.pitch = pitchLevel;
			engineSoundLow.volume = lowRPM * volumeLevel;
			engineSoundLow.pitch = pitchLevel;

			engineSoundHighOff.volume = highRPM * (1f - volumeLevel);
			engineSoundHighOff.pitch = pitchLevel;
			engineSoundLowOff.volume = lowRPM * (1f - volumeLevel);
			engineSoundLowOff.pitch = pitchLevel;

			if(engineSoundIdle){

				engineSoundIdle.volume = Mathf.Lerp(engineRunning ? idleEngineSoundVolume : 0f, 0f, engineRPM / maxEngineRPM) * speedVolumeReduction;
				engineSoundIdle.pitch = pitchLevel;

			}

			if(!engineSoundLow.isPlaying)
				engineSoundLow.Play();
			if(!engineSoundHigh.isPlaying)
				engineSoundHigh.Play();
			if(!engineSoundIdle.isPlaying)
				engineSoundIdle.Play();

			break;

		case RCC_CarControllerV3.AudioType.ThreeSource:

			engineSoundHigh.volume = highRPM * volumeLevel;
			engineSoundHigh.pitch = pitchLevel;
			engineSoundMed.volume = medRPM * volumeLevel;
			engineSoundMed.pitch = pitchLevel;
			engineSoundLow.volume = lowRPM * volumeLevel;
			engineSoundLow.pitch = pitchLevel;

			engineSoundHighOff.volume = highRPM * (1f - volumeLevel);
			engineSoundHighOff.pitch = pitchLevel;
			engineSoundMedOff.volume = medRPM * (1f - volumeLevel);
			engineSoundMedOff.pitch = pitchLevel;
			engineSoundLowOff.volume = lowRPM * (1f - volumeLevel);
			engineSoundLowOff.pitch = pitchLevel;

			if(engineSoundIdle){

				engineSoundIdle.volume = Mathf.Lerp(engineRunning ? idleEngineSoundVolume : 0f, 0f, engineRPM / maxEngineRPM) * speedVolumeReduction;
				engineSoundIdle.pitch = pitchLevel;

			}

			if(!engineSoundLow.isPlaying)
				engineSoundLow.Play();
			if(!engineSoundMed.isPlaying)
				engineSoundMed.Play();
			if(!engineSoundHigh.isPlaying)
				engineSoundHigh.Play();
			if(!engineSoundIdle.isPlaying)
				engineSoundIdle.Play();
			
			break;

		}

	}

	/// <summary>
	/// Antiroll bars.
	/// </summary>
	private void AntiRollBars (){

		#region Horizontal

		WheelHit FrontWheelHit;
		
		float travelFL = 1.0f;
		float travelFR = 1.0f;
		
		bool groundedFL= FrontLeftWheelCollider.wheelCollider.GetGroundHit(out FrontWheelHit);
		
		if (groundedFL)
			travelFL = (-FrontLeftWheelCollider.transform.InverseTransformPoint(FrontWheelHit.point).y - FrontLeftWheelCollider.wheelCollider.radius) / FrontLeftWheelCollider.wheelCollider.suspensionDistance;
		
		bool groundedFR= FrontRightWheelCollider.wheelCollider.GetGroundHit(out FrontWheelHit);
		
		if (groundedFR)
			travelFR = (-FrontRightWheelCollider.transform.InverseTransformPoint(FrontWheelHit.point).y - FrontRightWheelCollider.wheelCollider.radius) / FrontRightWheelCollider.wheelCollider.suspensionDistance;
		
		float antiRollForceFrontHorizontal= (travelFL - travelFR) * antiRollFrontHorizontal;
		
		if (groundedFL)
			rigid.AddForceAtPosition(FrontLeftWheelCollider.transform.up * -antiRollForceFrontHorizontal, FrontLeftWheelCollider.transform.position); 
		if (groundedFR)
			rigid.AddForceAtPosition(FrontRightWheelCollider.transform.up * antiRollForceFrontHorizontal, FrontRightWheelCollider.transform.position); 
		
		WheelHit RearWheelHit;

		float travelRL = 1.0f;
		float travelRR = 1.0f;
		
		bool groundedRL= RearLeftWheelCollider.wheelCollider.GetGroundHit(out RearWheelHit);
		
		if (groundedRL)
			travelRL = (-RearLeftWheelCollider.transform.InverseTransformPoint(RearWheelHit.point).y - RearLeftWheelCollider.wheelCollider.radius) / RearLeftWheelCollider.wheelCollider.suspensionDistance;
		
		bool groundedRR= RearRightWheelCollider.wheelCollider.GetGroundHit(out RearWheelHit);
		
		if (groundedRR)
			travelRR = (-RearRightWheelCollider.transform.InverseTransformPoint(RearWheelHit.point).y - RearRightWheelCollider.wheelCollider.radius) / RearRightWheelCollider.wheelCollider.suspensionDistance;
		
		float antiRollForceRearHorizontal= (travelRL - travelRR) * antiRollRearHorizontal;
		
		if (groundedRL)
			rigid.AddForceAtPosition(RearLeftWheelCollider.transform.up * -antiRollForceRearHorizontal, RearLeftWheelCollider.transform.position); 
		if (groundedRR)
			rigid.AddForceAtPosition(RearRightWheelCollider.transform.up * antiRollForceRearHorizontal, RearRightWheelCollider.transform.position);
		
		#endregion

		#region Vertical

		float antiRollForceFrontVertical= (travelFL - travelRL) * antiRollVertical;

		if (groundedFL)
			rigid.AddForceAtPosition(FrontLeftWheelCollider.transform.up * -antiRollForceFrontVertical, FrontLeftWheelCollider.transform.position); 
		if (groundedRL)
			rigid.AddForceAtPosition(RearLeftWheelCollider.transform.up * antiRollForceFrontVertical, RearLeftWheelCollider.transform.position); 

		float antiRollForceRearVertical= (travelFR - travelRR) * antiRollVertical;

		if (groundedFR)
			rigid.AddForceAtPosition(FrontRightWheelCollider.transform.up * -antiRollForceRearVertical, FrontRightWheelCollider.transform.position); 
		if (groundedRR)
			rigid.AddForceAtPosition(RearRightWheelCollider.transform.up * antiRollForceRearVertical, RearRightWheelCollider.transform.position); 

		#endregion

	}

	/// <summary>
	/// Steering helper.
	/// </summary>
	private void SteerHelper(){

		if (!steeringDirection || !velocityDirection) {

			if (!steeringDirection) {

				GameObject steeringDirectionGO = new GameObject ("Steering Direction");
				steeringDirectionGO.transform.SetParent (transform, false);
				steeringDirection = steeringDirectionGO.transform;
				steeringDirectionGO.transform.localPosition = new Vector3 (1f, 2f, 0f);
				steeringDirectionGO.transform.localScale = new Vector3 (.1f, .1f, 3f);

			}

			if (!velocityDirection) {

				GameObject velocityDirectionGO = new GameObject ("Velocity Direction");
				velocityDirectionGO.transform.SetParent (transform, false);
				velocityDirection = velocityDirectionGO.transform;
				velocityDirectionGO.transform.localPosition = new Vector3 (-1f, 2f, 0f);
				velocityDirectionGO.transform.localScale = new Vector3 (.1f, .1f, 3f);

			}

			return;

		}

		for (int i = 0; i < allWheelColliders.Length; i++){

			WheelHit hit;
			allWheelColliders[i].wheelCollider.GetGroundHit(out hit);
			if (hit.normal == Vector3.zero)
				return;

		}

		Vector3 v = rigid.angularVelocity;
		velocityAngle = (v.y * Mathf.Clamp(transform.InverseTransformDirection(rigid.velocity).z, -1f, 1f)) * Mathf.Rad2Deg;
		velocityDirection.localRotation = Quaternion.Lerp(velocityDirection.localRotation, Quaternion.AngleAxis(Mathf.Clamp(velocityAngle / 3f, -45f, 45f), Vector3.up), Time.fixedDeltaTime * 20f);
		steeringDirection.localRotation = Quaternion.Euler (0f, FrontLeftWheelCollider.wheelCollider.steerAngle, 0f);

		int normalizer = 1;

		if (steeringDirection.localRotation.y > velocityDirection.localRotation.y)
			normalizer = 1;
		else
			normalizer = -1;

		float angle2 = Quaternion.Angle (velocityDirection.localRotation, steeringDirection.localRotation) * (normalizer);

		rigid.AddRelativeTorque (Vector3.up * ((angle2 * (Mathf.Clamp(transform.InverseTransformDirection(rigid.velocity).z, -10f, 10f) / 1000f)) * steerHelperAngularVelStrength), ForceMode.VelocityChange);

		if (Mathf.Abs(oldRotation - transform.eulerAngles.y) < 10f){

			float turnadjust = (transform.eulerAngles.y - oldRotation) * (steerHelperLinearVelStrength / 2f);
			Quaternion velRotation = Quaternion.AngleAxis(turnadjust, Vector3.up);
			rigid.velocity = (velRotation * rigid.velocity);

		}

		oldRotation = transform.eulerAngles.y;

	}

	/// <summary>
	/// Traction helper.
	/// </summary>
	private void TractionHelper(){

		Vector3 velocity = rigid.velocity;
		velocity -= transform.up * Vector3.Dot(velocity, transform.up);
		velocity.Normalize();

		angle = -Mathf.Asin(Vector3.Dot(Vector3.Cross(transform.forward, velocity), transform.up));

		angularVelo = rigid.angularVelocity.y;

		if (angle * FrontLeftWheelCollider.wheelCollider.steerAngle < 0) {
			FrontLeftWheelCollider.tractionHelpedSidewaysStiffness = (1f - Mathf.Clamp01 (tractionHelperStrength * Mathf.Abs (angularVelo)));
		} else {
			FrontLeftWheelCollider.tractionHelpedSidewaysStiffness = 1f;
		}

		if (angle * FrontRightWheelCollider.wheelCollider.steerAngle < 0) {
			FrontRightWheelCollider.tractionHelpedSidewaysStiffness = (1f - Mathf.Clamp01 (tractionHelperStrength * Mathf.Abs (angularVelo)));
		} else {
			FrontRightWheelCollider.tractionHelpedSidewaysStiffness = 1f;
		}

	}

	/// <summary>
	/// Angular drag helper.
	/// </summary>
	private void AngularDragHelper(){

		rigid.angularDrag = Mathf.Lerp (0f, 10f, (speed * angularDragHelperStrength) / 1000f);

	}

	/// <summary>
	/// Clutch.
	/// </summary>
	private void Clutch(){

		if(engineRunning)
			idleInput = Mathf.Lerp(1f, 0f, engineRPM / minEngineRPM);
		else
			idleInput = 0f;

		if (currentGear == 0) {

			if (useClutchMarginAtFirstGear) {
				
				if (launched >= .25f)
					clutchInput = Mathf.Lerp (clutchInput, (Mathf.Lerp (1f, (Mathf.Lerp (clutchInertia, 0f, ((RearLeftWheelCollider.wheelRPMToSpeed + RearRightWheelCollider.wheelRPMToSpeed) / 2f) / gears [0].targetSpeedForNextGear)), Mathf.Abs (_gasInput))), Time.fixedDeltaTime * 5f);
				else
					clutchInput = Mathf.Lerp (clutchInput, 1f / speed, Time.fixedDeltaTime * 5f);
				
			} else {
				
				clutchInput = Mathf.Lerp (clutchInput, (Mathf.Lerp (1f, (Mathf.Lerp (clutchInertia, 0f, ((RearLeftWheelCollider.wheelRPMToSpeed + RearRightWheelCollider.wheelRPMToSpeed) / 2f) / gears [0].targetSpeedForNextGear)), Mathf.Abs (_gasInput))), Time.fixedDeltaTime * 5f);

			}
			
		} else {
			
			if (changingGear)
				clutchInput = Mathf.Lerp (clutchInput, 1, Time.fixedDeltaTime * 5f);
			else
				clutchInput = Mathf.Lerp (clutchInput, 0, Time.fixedDeltaTime * 5f);

		} 

		if(cutGas || handbrakeInput >= .1f)
			clutchInput = 1f;

		if (NGear)
			clutchInput = 1f;

		clutchInput = Mathf.Clamp01(clutchInput);

	}

	/// <summary>
	/// Gearbox.
	/// </summary>
	private void GearBox (){

		//Reversing Bool.
		if(!externalController){
			
			if(brakeInput > .9f  && transform.InverseTransformDirection(rigid.velocity).z < 1f && canGoReverseNow && automaticGear && !semiAutomaticGear && !changingGear && direction != -1)
				StartCoroutine(ChangeGear(-1));
			else if(brakeInput < .1f && transform.InverseTransformDirection(rigid.velocity).z > -1f && direction == -1 && !changingGear && automaticGear && !semiAutomaticGear)
				StartCoroutine(ChangeGear(0));
			
		}

		if(automaticGear){

			if(currentGear < gears.Length - 1 && !changingGear){
				if(speed >= (gears[currentGear].targetSpeedForNextGear * .9f) && FrontLeftWheelCollider.wheelCollider.rpm > 0){
					if(!semiAutomaticGear)
						StartCoroutine(ChangeGear(currentGear + 1));
					else if(semiAutomaticGear && direction != -1)
						StartCoroutine(ChangeGear(currentGear + 1));
				}
			}
			
			if(currentGear > 0){

				if(!changingGear){

					if(speed < (gears[currentGear - 1].targetSpeedForNextGear * .7f) && direction != -1){
						StartCoroutine(ChangeGear(currentGear - 1));
					}

				}

			}
			
		}

		if(direction == -1){

			if(!reversingSound.isPlaying)
				reversingSound.Play();

			// Apply balanced volume for reversing sound with speed limit
			float reverseVolumeLevel = Mathf.Lerp(0f, 0.8f, speed / gears[0].targetSpeedForNextGear); // Limit max volume to 80%
			reversingSound.volume = reverseVolumeLevel;
			// More stable pitch calculation for reverse sound
			float reversePitch = Mathf.Lerp(0.8f, 1.1f, Mathf.Clamp01(speed / gears[0].targetSpeedForNextGear));
			reversingSound.pitch = Mathf.Clamp(reversePitch, 0.8f, 1.1f); // Narrower, more stable pitch range

		}else{

			if(reversingSound.isPlaying)
				reversingSound.Stop();

			reversingSound.volume = 0f;
			reversingSound.pitch = 0f;

		}
		
	}

	/// <summary>
	/// Changes the gear.
	/// </summary>
	/// <returns>The gear.</returns>
	/// <param name="gear">Gear.</param>
	public IEnumerator ChangeGear(int gear){

		changingGear = true;

		if(RCCSettings.useTelemetry)
			print ("Shifted to: " + (gear).ToString()); 

		if(gearShiftingClips.Length > 0){
			
			gearShiftingSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Gear Shifting AudioSource", 1f, 5f, RCCSettings.maxGearShiftingSoundVolume, gearShiftingClips[UnityEngine.Random.Range(0, gearShiftingClips.Length)], false, true, true);

			if(!gearShiftingSound.isPlaying)
				gearShiftingSound.Play();
			
		}
		
		yield return new WaitForSeconds(gearShiftingDelay);

		if(gear == -1){
			
			currentGear = 0;

			if(!NGear)
				direction = -1;
			else
				direction = 0;

		}else{
			
			currentGear = gear;

			if(!NGear)
				direction = 1;
			else
				direction = 0;

		}

		changingGear = false;

	}

	/// <summary>
	/// Gears the shift up.
	/// </summary>
	public void GearShiftUp(){

		if(currentGear < gears.Length - 1 && !changingGear){

			if(direction != -1)
				StartCoroutine(ChangeGear(currentGear + 1));
			else
				StartCoroutine(ChangeGear(0));

		}

	}

	/// <summary>
	/// Gears the shift down.
	/// </summary>
	public void GearShiftDown(){

		if(currentGear >= 0)
			StartCoroutine(ChangeGear(currentGear - 1));	

	}

	/// <summary>
	/// Rev limiter.
	/// </summary>
	private void RevLimiter(){

		if((useRevLimiter && engineRPM >= maxEngineRPM))
			cutGas = true;
		else if(engineRPM < (maxEngineRPM * .95f))
			cutGas = false;
		
	}

	/// <summary>
	/// NOS.
	/// </summary>
	private void NOS(){

		if(!useNOS)
			return;

		if(!NOSSound)
			NOSSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "NOS Sound AudioSource", 5, 10, 1f, NOSClip, true, false, false);

		if(!blowSound)
			blowSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "NOS Blow", 1, 10, 1, null, false, false, false);

		if(boostInput >= .8f && _gasInput >= .8f && NoS > 5){
			
			NoS -= NoSConsumption * Time.fixedDeltaTime;
			NoSRegenerateTime = 0f;

			if(!NOSSound.isPlaying)
				NOSSound.Play();
			
		}else{
			
			if(NoS < 100 && NoSRegenerateTime > 3)
				NoS += (NoSConsumption / 1.5f) * Time.fixedDeltaTime;
			
			NoSRegenerateTime += Time.fixedDeltaTime;

			if(NOSSound.isPlaying){
				
				NOSSound.Stop();
				blowSound.clip = RCCSettings.blowoutClip[UnityEngine.Random.Range(0, RCCSettings.blowoutClip.Length)];
				blowSound.Play();

			}

		}

	}

	/// <summary>
	/// Turbo.
	/// </summary>
	private void Turbo(){

		if(!useTurbo)
			return;

		if (!turboSound) {
			
			turboSound = RCC_CreateAudioSource.NewAudioSource (gameObject, "Turbo Sound AudioSource", .1f, .5f, 0, turboClip, true, true, false);
			RCC_CreateAudioSource.NewHighPassFilter (turboSound, 10000f, 10);

		}

		turboBoost = Mathf.Lerp(turboBoost, Mathf.Clamp(Mathf.Pow(_gasInput, 10) * 30f + Mathf.Pow(engineRPM / maxEngineRPM, 10) * 30f, 0f, 30f), Time.fixedDeltaTime * 10f);

		if(turboBoost >= 25f){
			
			if(turboBoost < (turboSound.volume * 30f)){
				
				if(!blowSound.isPlaying){
					
					blowSound.clip = RCCSettings.blowoutClip[UnityEngine.Random.Range(0, RCCSettings.blowoutClip.Length)];
					blowSound.Play();

				}

			}

		}

		turboSound.volume = Mathf.Lerp(turboSound.volume, turboBoost / 30f, Time.fixedDeltaTime * 5f);
		turboSound.pitch = Mathf.Lerp(Mathf.Clamp(turboSound.pitch, 2f, 3f), (turboBoost / 30f) * 2f, Time.fixedDeltaTime * 5f);

	}

	/// <summary>
	/// Fuel.
	/// </summary>
	private void Fuel(){

		fuelTank -= ((engineRPM / 10000f) * fuelConsumptionRate) * Time.fixedDeltaTime;
		fuelTank = Mathf.Clamp (fuelTank, 0f, fuelTankCapacity);

	}

	/// <summary>
	/// Engine heat.
	/// </summary>
	private void EngineHeat(){

		engineHeat += ((engineRPM / 10000f) * engineHeatRate) * Time.fixedDeltaTime;

		if (engineHeat > engineCoolingWaterThreshold)
			engineHeat -= engineCoolRate * Time.fixedDeltaTime;

		engineHeat -= (engineCoolRate / 10f) * Time.fixedDeltaTime;

		engineHeat = Mathf.Clamp (engineHeat, 15f, 120f);

	}

	/// <summary>
	/// Drift variables.
	/// </summary>
	private void DriftVariables(){
		
		WheelHit hit;
		RearRightWheelCollider.wheelCollider.GetGroundHit(out hit);

		if(Mathf.Abs(hit.sidewaysSlip) > .25f)
			driftingNow = true;
		else
			driftingNow = false;
		
		if(speed > 10f)
			driftAngle = hit.sidewaysSlip * .75f;
		else
			driftAngle = 0f;
		
	}

	/// <summary>
	/// Resets the car.
	/// </summary>
	private void ResetCar (){
		
		if(speed < 5 && !rigid.isKinematic){

			if (!RCCSettings.autoReset)
				return; 
			
			if(transform.eulerAngles.z < 300 && transform.eulerAngles.z > 60){
				resetTime += Time.deltaTime;
				if(resetTime > 3){
					transform.rotation = Quaternion.Euler (0f, transform.eulerAngles.y, 0f);
					transform.position = new Vector3(transform.position.x, transform.position.y + 3, transform.position.z);
					resetTime = 0f;
				}
			}
			
		}
		
	}

	/// <summary>
	/// Raises the collision enter event.
	/// </summary>
	/// <param name="collision">Collision.</param>
	void OnCollisionEnter (Collision collision){
		
		if (collision.contacts.Length < 1 || collision.relativeVelocity.magnitude < minimumCollisionForce)
			return;

		if(OnRCCPlayerCollision != null && this == RCC_SceneManager.Instance.activePlayerVehicle)
			OnRCCPlayerCollision (this, collision);

		if(useDamage){

			if (((1 << collision.gameObject.layer) & damageFilter) != 0) {
				
				CollisionParticles (collision.contacts [0].point);
			
				Vector3 colRelVel = collision.relativeVelocity;
				colRelVel *= 1f - Mathf.Abs (Vector3.Dot (transform.up, collision.contacts [0].normal));
			
				float cos = Mathf.Abs (Vector3.Dot (collision.contacts [0].normal, colRelVel.normalized));

				if (colRelVel.magnitude * cos >= minimumCollisionForce) {
				
					repaired = false;
				
					localVector = transform.InverseTransformDirection (colRelVel) * (damageMultiplier / 50f);

					if (originalMeshData == null)
						LoadOriginalMeshData ();
				
					for (int i = 0; i < deformableMeshFilters.Length; i++)
						DeformMesh (deformableMeshFilters [i].mesh, originalMeshData [i].meshVerts, collision, cos, deformableMeshFilters [i].transform, rot);
				
				}

			}

		}

		if(crashClips.Length > 0){

			if (collision.contacts[0].thisCollider.gameObject.transform != transform.parent){

				crashSound = RCC_CreateAudioSource.NewAudioSource(gameObject, "Crash Sound AudioSource", 5, 20, RCCSettings.maxCrashSoundVolume, crashClips[UnityEngine.Random.Range(0, crashClips.Length)], false, true, true);

				if(!crashSound.isPlaying)
					crashSound.Play();

			}

		}

	}

	/// <summary>
	/// Raises the draw gizmos event.
	/// </summary>
	void OnDrawGizmos(){
#if UNITY_EDITOR
		if(Application.isPlaying){

			WheelHit hit;

			for(int i = 0; i < allWheelColliders.Length; i++){

				allWheelColliders[i].wheelCollider.GetGroundHit(out hit);

				Matrix4x4 temp = Gizmos.matrix;
				Gizmos.matrix = Matrix4x4.TRS(allWheelColliders[i].transform.position, Quaternion.AngleAxis(-90, Vector3.right), Vector3.one);
				Gizmos.color = new Color((hit.force / rigid.mass) / 5f, (-hit.force / rigid.mass) / 5f, 0f);
				Gizmos.DrawFrustum(Vector3.zero, 2f, hit.force / rigid.mass, .1f, 1f);
				Gizmos.matrix = temp;

			}

		}
#endif
	}


	/// <summary>
	/// Previews the smoke particle.
	/// </summary>
	/// <param name="state">If set to <c>true</c> state.</param>
	public void PreviewSmokeParticle(bool state){

		canControl = state;
		permanentGas = state;
		rigid.isKinematic = state;

	}

	/// <summary>
	/// Detachs the trailer.
	/// </summary>
	public void DetachTrailer(){

		if (!attachedTrailer)
			return;

		attachedTrailer.DetachTrailer ();

	}

	/// <summary>
	/// Raises the destroy event.
	/// </summary>
	void OnDestroy(){

		if (OnRCCPlayerDestroyed != null)
			OnRCCPlayerDestroyed (this);

		if(canControl){
			
			if(gameObject.GetComponentInChildren<RCC_Camera>())
				gameObject.GetComponentInChildren<RCC_Camera>().transform.SetParent(null);
			
		}

	}

	/// <summary>
	/// Sets the can control.
	/// </summary>
	/// <param name="state">If set to <c>true</c> state.</param>
	public void SetCanControl(bool state){

		canControl = state;

	}

	/// <summary>
	/// Sets the engine state.
	/// </summary>
	/// <param name="state">If set to <c>true</c> state.</param>
	public void SetEngine(bool state){

		if (state)
			StartEngine ();
		else
			KillEngine ();

	}

	void OnDisable(){

		RCC_SceneManager.OnBehaviorChanged -= CheckBehavior;

	}
	
} 
