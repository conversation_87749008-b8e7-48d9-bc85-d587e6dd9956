%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7756f06ac1b082e4bb1697f768a5a41f, type: 3}
  m_Name: RCC_Settings
  m_EditorClassIdentifier: 
  RCCVersion: 3.3
  controllerSelectedIndex: 1
  behaviorSelectedIndex: 4
  overrideFixedTimeStep: 1
  fixedTimeStep: 0.02
  maxAngularVelocity: 8
  overrideBehavior: 1
  behaviorTypes:
  - behaviorName: Simulator
    steeringHelper: 1
    tractionHelper: 1
    ABS: 1
    ESP: 1
    TCS: 1
    applyExternalWheelFrictions: 0
    applyRelativeTorque: 0
    highSpeedSteerAngleMinimum: 0
    highSpeedSteerAngleMaximum: 60
    highSpeedSteerAngleAtspeedMinimum: 0
    highSpeedSteerAngleAtspeedMaximum: 350
    steerHelperAngularVelStrengthMinimum: 0.1
    steerHelperAngularVelStrengthMaximum: 0.1
    steerHelperLinearVelStrengthMinimum: 0.1
    steerHelperLinearVelStrengthMaximum: 0.1
    tractionHelperStrengthMinimum: 0.1
    tractionHelperStrengthMaximum: 0.1
    antiRollFrontHorizontalMinimum: 1000
    antiRollRearHorizontalMinimum: 1000
    gearShiftingDelayMaximum: 0.15
    angularDrag: 0.5
    forwardExtremumSlip: 0.2
    forwardExtremumValue: 1
    forwardAsymptoteSlip: 0.8
    forwardAsymptoteValue: 0.75
    sidewaysExtremumSlip: 0.25
    sidewaysExtremumValue: 1
    sidewaysAsymptoteSlip: 0.5
    sidewaysAsymptoteValue: 0.75
  - behaviorName: Racing
    steeringHelper: 1
    tractionHelper: 1
    ABS: 1
    ESP: 1
    TCS: 1
    applyExternalWheelFrictions: 0
    applyRelativeTorque: 0
    highSpeedSteerAngleMinimum: 0
    highSpeedSteerAngleMaximum: 60
    highSpeedSteerAngleAtspeedMinimum: 0
    highSpeedSteerAngleAtspeedMaximum: 350
    steerHelperAngularVelStrengthMinimum: 0.25
    steerHelperAngularVelStrengthMaximum: 1
    steerHelperLinearVelStrengthMinimum: 0.25
    steerHelperLinearVelStrengthMaximum: 1
    tractionHelperStrengthMinimum: 0.25
    tractionHelperStrengthMaximum: 1
    antiRollFrontHorizontalMinimum: 5000
    antiRollRearHorizontalMinimum: 5000
    gearShiftingDelayMaximum: 0.15
    angularDrag: 0.5
    forwardExtremumSlip: 0.2
    forwardExtremumValue: 1
    forwardAsymptoteSlip: 0.8
    forwardAsymptoteValue: 0.75
    sidewaysExtremumSlip: 0.3
    sidewaysExtremumValue: 1
    sidewaysAsymptoteSlip: 0.25
    sidewaysAsymptoteValue: 0.75
  - behaviorName: Drift
    steeringHelper: 1
    tractionHelper: 1
    ABS: 0
    ESP: 0
    TCS: 0
    applyExternalWheelFrictions: 1
    applyRelativeTorque: 1
    highSpeedSteerAngleMinimum: 40
    highSpeedSteerAngleMaximum: 60
    highSpeedSteerAngleAtspeedMinimum: 100
    highSpeedSteerAngleAtspeedMaximum: 200
    steerHelperAngularVelStrengthMinimum: 0
    steerHelperAngularVelStrengthMaximum: 0.01
    steerHelperLinearVelStrengthMinimum: 0
    steerHelperLinearVelStrengthMaximum: 0.025
    tractionHelperStrengthMinimum: 0
    tractionHelperStrengthMaximum: 0.5
    antiRollFrontHorizontalMinimum: 1000
    antiRollRearHorizontalMinimum: 1000
    gearShiftingDelayMaximum: 0.15
    angularDrag: 0.5
    forwardExtremumSlip: 0.3
    forwardExtremumValue: 1
    forwardAsymptoteSlip: 0.8
    forwardAsymptoteValue: 1
    sidewaysExtremumSlip: 0.3
    sidewaysExtremumValue: 1
    sidewaysAsymptoteSlip: 0.5
    sidewaysAsymptoteValue: 1
  - behaviorName: Semi Arcade
    steeringHelper: 1
    tractionHelper: 1
    ABS: 0
    ESP: 0
    TCS: 0
    applyExternalWheelFrictions: 0
    applyRelativeTorque: 0
    highSpeedSteerAngleMinimum: 0
    highSpeedSteerAngleMaximum: 60
    highSpeedSteerAngleAtspeedMinimum: 100
    highSpeedSteerAngleAtspeedMaximum: 350
    steerHelperAngularVelStrengthMinimum: 0.5
    steerHelperAngularVelStrengthMaximum: 1
    steerHelperLinearVelStrengthMinimum: 0.5
    steerHelperLinearVelStrengthMaximum: 1
    tractionHelperStrengthMinimum: 0.25
    tractionHelperStrengthMaximum: 1
    antiRollFrontHorizontalMinimum: 10000
    antiRollRearHorizontalMinimum: 10000
    gearShiftingDelayMaximum: 0.1
    angularDrag: 0.5
    forwardExtremumSlip: 0.2
    forwardExtremumValue: 2
    forwardAsymptoteSlip: 2
    forwardAsymptoteValue: 2
    sidewaysExtremumSlip: 0.25
    sidewaysExtremumValue: 2
    sidewaysAsymptoteSlip: 2
    sidewaysAsymptoteValue: 2
  - behaviorName: Fun
    steeringHelper: 1
    tractionHelper: 1
    ABS: 0
    ESP: 0
    TCS: 0
    applyExternalWheelFrictions: 0
    applyRelativeTorque: 0
    highSpeedSteerAngleMinimum: 0
    highSpeedSteerAngleMaximum: 60
    highSpeedSteerAngleAtspeedMinimum: 100
    highSpeedSteerAngleAtspeedMaximum: 350
    steerHelperAngularVelStrengthMinimum: 1
    steerHelperAngularVelStrengthMaximum: 1
    steerHelperLinearVelStrengthMinimum: 1
    steerHelperLinearVelStrengthMaximum: 1
    tractionHelperStrengthMinimum: 0.25
    tractionHelperStrengthMaximum: 1
    antiRollFrontHorizontalMinimum: 20000
    antiRollRearHorizontalMinimum: 20000
    gearShiftingDelayMaximum: 0.5
    angularDrag: 0.5
    forwardExtremumSlip: 0.2
    forwardExtremumValue: 2
    forwardAsymptoteSlip: 2
    forwardAsymptoteValue: 2
    sidewaysExtremumSlip: 0.25
    sidewaysExtremumValue: 2
    sidewaysAsymptoteSlip: 2
    sidewaysAsymptoteValue: 2
  useFixedWheelColliders: 1
  lockAndUnlockCursor: 0
  controllerType: 1
  verticalInput: Vertical
  horizontalInput: Horizontal
  mouseXInput: Mouse X
  mouseYInput: Mouse Y
  handbrakeKB: 32
  startEngineKB: 105
  lowBeamHeadlightsKB: 108
  highBeamHeadlightsKB: 107
  rightIndicatorKB: 101
  leftIndicatorKB: 113
  hazardIndicatorKB: 122
  shiftGearUp: 304
  shiftGearDown: 306
  NGear: 110
  boostKB: 102
  slowMotionKB: 103
  changeCameraKB: 99
  recordKB: 114
  playbackKB: 112
  lookBackKB: 98
  trailerAttachDetach: 116
  Xbox_verticalInput: Xbox_Vertical
  Xbox_horizontalInput: Xbox_Horizontal
  Xbox_triggerLeftInput: Xbox_TriggerLeft
  Xbox_triggerRightInput: Xbox_TriggerRight
  Xbox_mouseXInput: Xbox_MouseX
  Xbox_mouseYInput: Xbox_MouseY
  Xbox_handbrakeKB: Xbox_B
  Xbox_startEngineKB: Xbox_Y
  Xbox_lowBeamHeadlightsKB: Xbox_LB
  Xbox_highBeamHeadlightsKB: Xbox_RB
  Xbox_indicatorKB: Xbox_DPadHorizontal
  Xbox_hazardIndicatorKB: Xbox_DPadVertical
  Xbox_shiftGearUp: Xbox_RB
  Xbox_shiftGearDown: Xbox_LB
  Xbox_boostKB: Xbox_A
  Xbox_changeCameraKB: Xbox_Back
  Xbox_lookBackKB: Xbox_ClickRight
  Xbox_trailerAttachDetach: Xbox_ClickLeft
  useVR: 0
  useAutomaticGear: 1
  runEngineAtAwake: 0
  autoReverse: 1
  autoReset: 1
  contactParticles: {fileID: 193332, guid: 863b5201aede3aa438ac7d962625d663, type: 3}
  units: 0
  uiType: 0
  useTelemetry: 0
  mobileController: 0
  UIButtonSensitivity: 5
  UIButtonGravity: 5
  gyroSensitivity: 2
  useLightsAsVertexLights: 1
  useLightProjectorForLightingEffect: 0
  setTagsAndLayers: 0
  RCCLayer: RCC
  RCCTag: Player
  tagAllChildrenGameobjects: 0
  chassisJoint: {fileID: 116354, guid: a7e7b1406cded0e4cb52224eb5dc2d80, type: 3}
  exhaustGas: {fileID: 104134, guid: 46992adcb9d56714eb84164c8a965492, type: 3}
  skidmarksManager: {fileID: 114786316056322602, guid: a107dc7aa77e84f4d8cf9888def4fe64, type: 3}
  projector: {fileID: 102982, guid: 7f7b6e33e52b82e43a57b7cc85ed5cf9, type: 3}
  projectorIgnoreLayer:
    serializedVersion: 2
    m_Bits: 0
  headLights: {fileID: 146832, guid: 660ec4b1fd79fa74096f0ab9e6cbd2ca, type: 3}
  brakeLights: {fileID: 136378, guid: 40648ef4ebef26d4c9cc0663c2f08e5e, type: 3}
  reverseLights: {fileID: 197258, guid: 188c477ab444228409c4a246c02a86bd, type: 3}
  indicatorLights: {fileID: 166118, guid: 580925c070bc6064c90563e08cb421f0, type: 3}
  mirrors: {fileID: 132014, guid: 42ab1ab32651fc74dad3e9cedf0b5270, type: 3}
  RCCMainCamera: {fileID: 11400656, guid: 9f32ea4a11500b344ae05427bdc21e05, type: 3}
  hoodCamera: {fileID: 171864, guid: a28e2d33ee4eec642be5568debd291ab, type: 3}
  cinematicCamera: {fileID: 1000014008404042, guid: 330409232e97fd84c9b2e696d6de0bd5, type: 3}
  RCCCanvas: {fileID: 102148, guid: 3c49455ff7757444fa4b466c3250f423, type: 3}
  dontUseAnyParticleEffects: 0
  dontUseChassisJoint: 0
  dontUseSkidmarks: 0
  gearShiftingClips:
  - {fileID: 8300000, guid: f5993d9cca0a2804f93ce20e421cbf04, type: 3}
  - {fileID: 8300000, guid: 51d3f215e07c15a41aab4038522df8d6, type: 3}
  - {fileID: 8300000, guid: f5993d9cca0a2804f93ce20e421cbf04, type: 3}
  - {fileID: 8300000, guid: af63e188bf4d51d458ed8680ad764a3b, type: 3}
  - {fileID: 8300000, guid: f5993d9cca0a2804f93ce20e421cbf04, type: 3}
  crashClips: []
  reversingClip: {fileID: 8300000, guid: 504366e8a8a713b43b676259cd5a7b58, type: 3}
  windClip: {fileID: 8300000, guid: 4b6a38decd073724fb0aebb87b9ae8ee, type: 3}
  brakeClip: {fileID: 8300000, guid: 4b6a38decd073724fb0aebb87b9ae8ee, type: 3}
  indicatorClip: {fileID: 8300000, guid: 64545518873b33e49b90fdf05c6ea7a6, type: 3}
  NOSClip: {fileID: 8300000, guid: 7f48c51367eb26f4bb55673f28ae5404, type: 3}
  turboClip: {fileID: 8300000, guid: 7f48c51367eb26f4bb55673f28ae5404, type: 3}
  blowoutClip:
  - {fileID: 8300000, guid: fe7d483597e1b604a8f6f21f68b9df07, type: 3}
  exhaustFlameClips:
  - {fileID: 8300000, guid: a8cfbfa0153a85c4da8402ee5479a3de, type: 3}
  - {fileID: 8300000, guid: abe24e1d59cd24e48ab2b17acb6b57ff, type: 3}
  - {fileID: 8300000, guid: 4ebe01570cad61a40be61693713688b4, type: 3}
  - {fileID: 8300000, guid: f772a702dcfbfe9478afe01aa9429fbc, type: 3}
  useSharedAudioSources: 0
  maxGearShiftingSoundVolume: 0.3
  maxCrashSoundVolume: 0.3
  maxWindSoundVolume: 0.5
  maxBrakeSoundVolume: 0.3
  foldGeneralSettings: 1
  foldBehaviorSettings: 1
  foldControllerSettings: 1
  foldUISettings: 1
  foldWheelPhysics: 1
  foldSFX: 0
  foldOptimization: 1
  foldTagsAndLayers: 1
