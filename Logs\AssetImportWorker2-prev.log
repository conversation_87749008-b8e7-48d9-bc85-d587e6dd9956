Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker2.log
-srvPort
50277
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20440] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1098606269 [EditorId] 1098606269 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [20440] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1098606269 [EditorId] 1098606269 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 112.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56516
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.013650 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 402 ms
Refreshing native plugins compatible for Editor in 83.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.998 seconds
Domain Reload Profiling:
	ReloadAssembly (1999ms)
		BeginReloadAssembly (161ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1440ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (471ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (847ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (517ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (84ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (161ms)
				ProcessInitializeOnLoadMethodAttributes (83ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016645 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.603 seconds
Domain Reload Profiling:
	ReloadAssembly (2604ms)
		BeginReloadAssembly (241ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (11ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (39ms)
		EndReloadAssembly (2168ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (105ms)
			SetupLoadedEditorAssemblies (1519ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (95ms)
				ProcessInitializeOnLoadAttributes (1349ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.13 seconds
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2305 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2717.
Memory consumption went from 122.1 MB to 121.4 MB.
Total: 7.330300 ms (FindLiveObjects: 0.347000 ms CreateObjectMapping: 0.168400 ms MarkObjects: 5.677500 ms  DeleteObjects: 1.136100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4141.872052 seconds.
  path: Assets/Magic Spells and Popup effects/Sounds/SFX_C6_Upgrade_Complete.wav
  artifactKey: Guid(5546f46d2faabec43b76f369a57a1427) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Magic Spells and Popup effects/Sounds/SFX_C6_Upgrade_Complete.wav using Guid(5546f46d2faabec43b76f369a57a1427) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b5762143f0501fbda972a0dcb5afd63d') in 0.263496 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Models/Cylinder.fbx
  artifactKey: Guid(4a8c064de5d1b4045a9e9d4ebd5d6575) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Models/Cylinder.fbx using Guid(4a8c064de5d1b4045a9e9d4ebd5d6575) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4da4ea3431de718c49ea975c266910ba') in 0.111068 seconds 
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Magic Spells and Popup effects/Particles/PS_C6_Upgrade_Complete.prefab
  artifactKey: Guid(3a2c31aec5bb1b74aa1f40466ed81ce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Magic Spells and Popup effects/Particles/PS_C6_Upgrade_Complete.prefab using Guid(3a2c31aec5bb1b74aa1f40466ed81ce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '69f8c2cf99124ffd9ffa77db52eab41f') in 0.087408 seconds 
Number of asset objects unloaded after import = 56
========================================================================
Received Import Request.
  Time since last request: 94.403898 seconds.
  path: Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab
  artifactKey: Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Hovl Studio/Magic effects pack/Prefabs/Magic circles/Check point.prefab using Guid(244ce8441218ba14aa5e3951a7d985ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '04c238076f7eb278c1a4135fa062bef6') in 0.115823 seconds 
Number of asset objects unloaded after import = 104
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013884 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.204 seconds
Domain Reload Profiling:
	ReloadAssembly (2205ms)
		BeginReloadAssembly (264ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (73ms)
		EndReloadAssembly (1784ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (324ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1200ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1059ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 157 unused Assets / (0.7 MB). Loaded Objects now: 2746.
Memory consumption went from 123.5 MB to 122.8 MB.
Total: 8.085100 ms (FindLiveObjects: 0.313900 ms CreateObjectMapping: 0.157400 ms MarkObjects: 6.286200 ms  DeleteObjects: 1.326100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1853.903754 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cde747e3a45a26bd8e4fe63d592d06f7') in 0.026985 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013500 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.213 seconds
Domain Reload Profiling:
	ReloadAssembly (2214ms)
		BeginReloadAssembly (237ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1816ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (314ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1246ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1104ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2761.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 8.540900 ms (FindLiveObjects: 0.551300 ms CreateObjectMapping: 0.201400 ms MarkObjects: 6.927900 ms  DeleteObjects: 0.858700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013926 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.170 seconds
Domain Reload Profiling:
	ReloadAssembly (2171ms)
		BeginReloadAssembly (230ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1772ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1211ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1070ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2776.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 8.568700 ms (FindLiveObjects: 0.721600 ms CreateObjectMapping: 0.225100 ms MarkObjects: 6.747000 ms  DeleteObjects: 0.873400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 661.571454 seconds.
  path: Assets/field/golden-texture-wheat-grain-seamless-163535862.jpg
  artifactKey: Guid(ead1e6953ee96604fae5fed6b3abe0ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/field/golden-texture-wheat-grain-seamless-163535862.jpg using Guid(ead1e6953ee96604fae5fed6b3abe0ca) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6b746b42ac686e29a7eac74e209a8454') in 0.111332 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013397 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.139 seconds
Domain Reload Profiling:
	ReloadAssembly (2140ms)
		BeginReloadAssembly (237ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1750ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (310ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1189ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1050ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2793.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 7.935700 ms (FindLiveObjects: 0.419000 ms CreateObjectMapping: 0.168600 ms MarkObjects: 6.395800 ms  DeleteObjects: 0.950600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 257.599731 seconds.
  path: Assets/Scenes/MAINMENU.unity
  artifactKey: Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/MAINMENU.unity using Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'df3d95083d1775c59ffe5c1368eef1c8') in 0.023562 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013701 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.194 seconds
Domain Reload Profiling:
	ReloadAssembly (2195ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1777ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (316ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1208ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (1070ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2808.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 7.079600 ms (FindLiveObjects: 0.321400 ms CreateObjectMapping: 0.158500 ms MarkObjects: 5.773200 ms  DeleteObjects: 0.824600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013944 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.20 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.295 seconds
Domain Reload Profiling:
	ReloadAssembly (2297ms)
		BeginReloadAssembly (279ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (71ms)
		EndReloadAssembly (1834ms)
			LoadAssemblies (173ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (310ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (30ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1116ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2823.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 7.569700 ms (FindLiveObjects: 0.456800 ms CreateObjectMapping: 0.187000 ms MarkObjects: 6.122300 ms  DeleteObjects: 0.801900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013922 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.213 seconds
Domain Reload Profiling:
	ReloadAssembly (2214ms)
		BeginReloadAssembly (259ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1786ms)
			LoadAssemblies (172ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (302ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (1234ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1089ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.92 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2838.
Memory consumption went from 123.7 MB to 123.0 MB.
Total: 6.972700 ms (FindLiveObjects: 0.377800 ms CreateObjectMapping: 0.184200 ms MarkObjects: 5.634500 ms  DeleteObjects: 0.774600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 17.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (0.7 MB). Loaded Objects now: 2838.
Memory consumption went from 77.3 MB to 76.6 MB.
Total: 8.593400 ms (FindLiveObjects: 0.347400 ms CreateObjectMapping: 0.167800 ms MarkObjects: 7.175600 ms  DeleteObjects: 0.901300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1773.383541 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '033a2e34189f9b78bbd4cb9779f93096') in 0.042785 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 137.002061 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7c072247eba8024d623801865ba24f55') in 0.124343 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 58.806340 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '35edc4be647019fc1fe5b6f957531d84') in 0.045069 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 6.992135 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '17098b9a7e703f9965e3e77207fec9e1') in 0.032536 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 25.984838 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '12008eb9e447ce935c08b2a64bb65b61') in 0.034634 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 6.383030 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fcf0c26eecc591aac5af26534c33a2c2') in 0.030867 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 13.010574 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '407af5968a80de81798b44605ef9a190') in 0.053435 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 6.871158 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61c41ad1f04b2974c3285f72e6aa08d0') in 0.057174 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 6.505084 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '14179ba53f8f22cac1d8d3ebda2dc1d3') in 0.049818 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 5.467406 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.jpg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.jpg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '818615b136d73ed8077deb68e31f1608') in 0.021557 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 63.958252 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0ff05a1fee97d0356f6107b394662e1c') in 0.087092 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 57.197399 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd9909542e953aa9db99cf5926ef58008') in 0.020273 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 8.111485 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f08ec5d772aac1f2370f15e84289f751') in 0.035973 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 189.492211 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a7f3165844f12091083be5816e8e6cd6') in 0.039348 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 21.592793 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0a166caab2dfb732288fbb3444017a98') in 0.031711 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 9.183514 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c042ea200cef5bf0aab05cc98a678c46') in 0.032072 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 10.317804 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd4e5a1ebce69a88d334f2c9f8f19cb8b') in 0.147840 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 52.431892 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '160ca922e75513065cbb94ceb6ced2ca') in 0.044694 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 27.658227 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a597ef6707e27151496f87c6a9db5f6') in 0.025774 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 14.679862 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f0b6c19406eb51cb55d2fcacab0aa4cd') in 0.090340 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 35.043954 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd46ccf334ebbc6eec1d8cfd41daa0cc') in 0.186345 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 30.542780 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '15501d6b7d6f0e93269684dec540144a') in 0.060473 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 16.191270 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '105c94702675664732a7fc0271ea0efa') in 0.036274 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 5.552377 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '43bd18f0335198796a83b3cb1d685840') in 0.074197 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 15.779645 seconds.
  path: Assets/Scenes/kachra/kachra/skybox.hdr
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/kachra/kachra/skybox.hdr using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '73e1ce96ff6cdea9a623573fe0f27e38') in 0.031800 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 850.497632 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ae9db89c85c0da913952cefadb87fd87') in 0.195994 seconds 
Number of asset objects unloaded after import = 74
========================================================================
Received Import Request.
  Time since last request: 25.747459 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '99b48e847160fa54a5a4e556cd6614bb') in 0.067886 seconds 
Number of asset objects unloaded after import = 70
========================================================================
Received Import Request.
  Time since last request: 5.575689 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '03c3f3b90608a88899d3f18d39449c0d') in 0.065025 seconds 
Number of asset objects unloaded after import = 75
========================================================================
Received Import Request.
  Time since last request: 2.151271 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.blend
  artifactKey: Guid(36908afc4af765d4e8c83e037f0b271e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.blend using Guid(36908afc4af765d4e8c83e037f0b271e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '076cc4e19a6d8531118b0407ae1e041e') in 0.119479 seconds 
Number of asset objects unloaded after import = 90
========================================================================
Received Import Request.
  Time since last request: 8.411814 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.blend
  artifactKey: Guid(36908afc4af765d4e8c83e037f0b271e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.blend using Guid(36908afc4af765d4e8c83e037f0b271e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fce3f14c3be0d46e59ccb8d1fc4f37cb') in 0.108971 seconds 
Number of asset objects unloaded after import = 91
========================================================================
Received Import Request.
  Time since last request: 44.002645 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b3156242b7387f7995dcf1dc80015729') in 0.115972 seconds 
Number of asset objects unloaded after import = 75
========================================================================
Received Import Request.
  Time since last request: 9.615693 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '25d317a7cccd80f6d0fab2666983b169') in 0.079991 seconds 
Number of asset objects unloaded after import = 75
========================================================================
Received Import Request.
  Time since last request: 9.210596 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '87777ca5f91ad5a5ed6e5142f2770cd6') in 0.059827 seconds 
Number of asset objects unloaded after import = 75
========================================================================
Received Import Request.
  Time since last request: 32.775096 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f1fe589e4462057645518e49325ade5') in 0.070012 seconds 
Number of asset objects unloaded after import = 76
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013825 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.333 seconds
Domain Reload Profiling:
	ReloadAssembly (2334ms)
		BeginReloadAssembly (337ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (123ms)
		EndReloadAssembly (1832ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (325ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1248ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1107ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2889.
Memory consumption went from 136.6 MB to 135.9 MB.
Total: 8.245700 ms (FindLiveObjects: 0.427800 ms CreateObjectMapping: 0.162300 ms MarkObjects: 6.906700 ms  DeleteObjects: 0.747700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 217.910266 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx
  artifactKey: Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow Animated Low-poly.fbx using Guid(9123418f045e0724e9c5a4e2faa11f71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '90a9825ae652f61252bc7fb94f9cd9f6') in 0.209449 seconds 
Number of asset objects unloaded after import = 76
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013577 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.223 seconds
Domain Reload Profiling:
	ReloadAssembly (2224ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1818ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (319ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1248ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1109ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2904.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 6.977800 ms (FindLiveObjects: 0.429500 ms CreateObjectMapping: 0.167000 ms MarkObjects: 5.612100 ms  DeleteObjects: 0.767800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 132.193095 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow.prefab
  artifactKey: Guid(70a541efde699a0438281bed6fcfd9fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow.prefab using Guid(70a541efde699a0438281bed6fcfd9fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6cfde1cc779ffdce13e37e4f1b02a198') in 0.163055 seconds 
Number of asset objects unloaded after import = 74
========================================================================
Received Import Request.
  Time since last request: 128.965752 seconds.
  path: Assets/Scarecrow Animated Low-poly/screcrowloo.fbx
  artifactKey: Guid(8f4bd7150589c4a41b5ef39da9714973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/screcrowloo.fbx using Guid(8f4bd7150589c4a41b5ef39da9714973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'efb26350c0b7972d2666996b2754db7d') in 0.098162 seconds 
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 7.315727 seconds.
  path: Assets/Scarecrow Animated Low-poly/screcrowloo.fbx
  artifactKey: Guid(8f4bd7150589c4a41b5ef39da9714973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/screcrowloo.fbx using Guid(8f4bd7150589c4a41b5ef39da9714973) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4cf9006244439576182273630dfaa841') in 0.070679 seconds 
Number of asset objects unloaded after import = 11
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013919 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.283 seconds
Domain Reload Profiling:
	ReloadAssembly (2284ms)
		BeginReloadAssembly (265ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1857ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (319ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1273ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1131ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2919.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 6.725500 ms (FindLiveObjects: 0.484800 ms CreateObjectMapping: 0.164000 ms MarkObjects: 5.327000 ms  DeleteObjects: 0.747800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013737 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.193 seconds
Domain Reload Profiling:
	ReloadAssembly (2194ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (1790ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (316ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1221ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1081ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.11 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2934.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 7.969100 ms (FindLiveObjects: 0.479900 ms CreateObjectMapping: 0.177300 ms MarkObjects: 5.999400 ms  DeleteObjects: 1.310800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013881 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.273 seconds
Domain Reload Profiling:
	ReloadAssembly (2274ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (52ms)
		EndReloadAssembly (1857ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1278ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1129ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (17ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2949.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 7.134200 ms (FindLiveObjects: 0.348500 ms CreateObjectMapping: 0.165800 ms MarkObjects: 5.765300 ms  DeleteObjects: 0.853500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 914.806686 seconds.
  path: Assets/Scarecrow Animated Low-poly
  artifactKey: Guid(490ca1961bfe98c4bbc76247cc64cb93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly using Guid(490ca1961bfe98c4bbc76247cc64cb93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41ba13e6452314c15e8337b00b1861f8') in 0.039464 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 18.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (0.7 MB). Loaded Objects now: 2949.
Memory consumption went from 89.5 MB to 88.8 MB.
Total: 8.756100 ms (FindLiveObjects: 0.487700 ms CreateObjectMapping: 0.174800 ms MarkObjects: 7.007200 ms  DeleteObjects: 1.084200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016078 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.288 seconds
Domain Reload Profiling:
	ReloadAssembly (2289ms)
		BeginReloadAssembly (261ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1859ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (341ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1248ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1100ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.09 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2964.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 8.312700 ms (FindLiveObjects: 0.434700 ms CreateObjectMapping: 0.185700 ms MarkObjects: 6.676900 ms  DeleteObjects: 1.013400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 14.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (0.7 MB). Loaded Objects now: 2964.
Memory consumption went from 90.4 MB to 89.7 MB.
Total: 9.737300 ms (FindLiveObjects: 0.493000 ms CreateObjectMapping: 0.189400 ms MarkObjects: 7.818200 ms  DeleteObjects: 1.235100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 915.647983 seconds.
  path: Assets/newterrianlayer/New Terrain 2.asset
  artifactKey: Guid(9c72f96bccfb42148ada1cb02b2fccd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/newterrianlayer/New Terrain 2.asset using Guid(9c72f96bccfb42148ada1cb02b2fccd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0f0352764dcf3ffdb2005365175c8206') in 0.115986 seconds 
Number of asset objects unloaded after import = 5
========================================================================
Received Import Request.
  Time since last request: 0.416307 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/Base.png
  artifactKey: Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/Base.png using Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '110c1748465afd66568666a2a47762a8') in 0.038852 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.579141 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png
  artifactKey: Guid(f03631af9f5d6bb4fb292ecd702d1eac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png using Guid(f03631af9f5d6bb4fb292ecd702d1eac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6ca154f4073e6ad5fcc1aa46849e5cd') in 0.032505 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 9.091672 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/Base.png
  artifactKey: Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/Base.png using Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0f7509314d658423e0f83b8732b6413') in 0.027769 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 6.042681 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/Base.png
  artifactKey: Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/Base.png using Guid(71f24281ef4b5354eba115dcb6e24dd1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '94e95bc6459e42b0094fafbba9521c63') in 0.322789 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 10.207485 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png
  artifactKey: Guid(f03631af9f5d6bb4fb292ecd702d1eac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Metalness.png using Guid(f03631af9f5d6bb4fb292ecd702d1eac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ab24f03624ce416736842b3b926b5051') in 0.008925 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 1.359322 seconds.
  path: Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Normal.png
  artifactKey: Guid(08f2c5423b88cbf4bb82d5dc332c4c32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/textures/ScareCrow_Bdy_Normal.png using Guid(08f2c5423b88cbf4bb82d5dc332c4c32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5c11915dbfc61b5bef13b5dc1483ac2e') in 0.034648 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 646.773952 seconds.
  path: Assets/Scarecrow Animated Low-poly/Scarecrow.prefab
  artifactKey: Guid(70a541efde699a0438281bed6fcfd9fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scarecrow Animated Low-poly/Scarecrow.prefab using Guid(70a541efde699a0438281bed6fcfd9fc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6dc4ed7ab6d14d2f2581071b8e04c19c') in 0.625405 seconds 
Number of asset objects unloaded after import = 80
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015631 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.486 seconds
Domain Reload Profiling:
	ReloadAssembly (2488ms)
		BeginReloadAssembly (456ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (19ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (165ms)
		EndReloadAssembly (1863ms)
			LoadAssemblies (194ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1241ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (1089ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2979.
Memory consumption went from 136.8 MB to 136.1 MB.
Total: 9.473300 ms (FindLiveObjects: 0.395200 ms CreateObjectMapping: 0.189200 ms MarkObjects: 7.955700 ms  DeleteObjects: 0.931900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015112 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.198 seconds
Domain Reload Profiling:
	ReloadAssembly (2199ms)
		BeginReloadAssembly (232ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1804ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (315ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1230ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1086ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2994.
Memory consumption went from 136.7 MB to 136.0 MB.
Total: 6.948000 ms (FindLiveObjects: 0.365900 ms CreateObjectMapping: 0.177000 ms MarkObjects: 5.617900 ms  DeleteObjects: 0.785800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4101.311018 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '07460f9ab6f55c1a9abe71d082c4bbde') in 0.072181 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016224 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.149 seconds
Domain Reload Profiling:
	ReloadAssembly (2150ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1747ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (320ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1171ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1030ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3009.
Memory consumption went from 136.9 MB to 136.2 MB.
Total: 7.112500 ms (FindLiveObjects: 0.350600 ms CreateObjectMapping: 0.166000 ms MarkObjects: 5.817600 ms  DeleteObjects: 0.776600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014072 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.177 seconds
Domain Reload Profiling:
	ReloadAssembly (2178ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1765ms)
			LoadAssemblies (171ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (318ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1182ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1036ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3024.
Memory consumption went from 136.9 MB to 136.2 MB.
Total: 7.091500 ms (FindLiveObjects: 0.359000 ms CreateObjectMapping: 0.162500 ms MarkObjects: 5.802500 ms  DeleteObjects: 0.766400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1188.945911 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1974f9beddb9b09bee12155ad2ac4091') in 0.012408 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4960.217288 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1d4acef1a0a2ce4ee9dc6027d737f4df') in 0.004217 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 9.813975 seconds.
  path: Assets/Scenes/MAINMENU.unity
  artifactKey: Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/MAINMENU.unity using Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e7b8115630c45eeb63dc71cefafffa2d') in 0.002757 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014127 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.281 seconds
Domain Reload Profiling:
	ReloadAssembly (2282ms)
		BeginReloadAssembly (302ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (93ms)
		EndReloadAssembly (1798ms)
			LoadAssemblies (179ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (331ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (75ms)
			SetupLoadedEditorAssemblies (1211ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1066ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3039.
Memory consumption went from 136.9 MB to 136.2 MB.
Total: 8.457800 ms (FindLiveObjects: 0.357100 ms CreateObjectMapping: 0.168800 ms MarkObjects: 7.151100 ms  DeleteObjects: 0.779600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015992 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.097 seconds
Domain Reload Profiling:
	ReloadAssembly (2098ms)
		BeginReloadAssembly (226ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1717ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (304ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1163ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1023ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (11ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3054.
Memory consumption went from 136.9 MB to 136.2 MB.
Total: 6.655600 ms (FindLiveObjects: 0.324600 ms CreateObjectMapping: 0.157900 ms MarkObjects: 5.412500 ms  DeleteObjects: 0.759300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 845.173433 seconds.
  path: Assets/Scenes/MAINMENU.unity
  artifactKey: Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/MAINMENU.unity using Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'afccf4464be23b824908fff261bd18f5') in 0.019965 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 769.232338 seconds.
  path: Assets/Scenes/MAINMENU.unity
  artifactKey: Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/MAINMENU.unity using Guid(b78dc1af455e45848bdd5268f9679b13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '463f9b3e4e4b100454aca8d89a541726') in 0.004061 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013703 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.229 seconds
Domain Reload Profiling:
	ReloadAssembly (2230ms)
		BeginReloadAssembly (255ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (1814ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (352ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (1206ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (1055ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3069.
Memory consumption went from 136.8 MB to 136.1 MB.
Total: 8.105700 ms (FindLiveObjects: 0.410000 ms CreateObjectMapping: 0.173300 ms MarkObjects: 6.315100 ms  DeleteObjects: 1.205700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013456 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.170 seconds
Domain Reload Profiling:
	ReloadAssembly (2171ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (53ms)
		EndReloadAssembly (1770ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (314ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (1213ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1069ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3084.
Memory consumption went from 137.0 MB to 136.3 MB.
Total: 7.514600 ms (FindLiveObjects: 0.394100 ms CreateObjectMapping: 0.192400 ms MarkObjects: 6.016900 ms  DeleteObjects: 0.910100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 197.146302 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '25904d97f6f64bb1823049aac9445581') in 0.023158 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 180.916091 seconds.
  path: Assets/arrows/Group 22 copy.png
  artifactKey: Guid(7627794ae6f08514488432ee91181188) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/arrows/Group 22 copy.png using Guid(7627794ae6f08514488432ee91181188) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e94062bc86286c67cf95254a1de4c44') in 0.089584 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 5.699584 seconds.
  path: Assets/arrows/Group 22 copy.png
  artifactKey: Guid(7627794ae6f08514488432ee91181188) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/arrows/Group 22 copy.png using Guid(7627794ae6f08514488432ee91181188) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '94485068f4618bcfcfa8daf971b0262f') in 0.054526 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 423.887235 seconds.
  path: Assets/Scenes/gameplay.unity
  artifactKey: Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Scenes/gameplay.unity using Guid(2f8ff117b3c359e4c8d4ca4d67ff6e2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c4bc16d7264ea04c7593e6169047a8e4') in 0.004038 seconds 
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013878 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.172 seconds
Domain Reload Profiling:
	ReloadAssembly (2173ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1758ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1184ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1040ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3102.
Memory consumption went from 137.0 MB to 136.3 MB.
Total: 7.024000 ms (FindLiveObjects: 0.373200 ms CreateObjectMapping: 0.171800 ms MarkObjects: 5.655900 ms  DeleteObjects: 0.821500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013688 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.151 seconds
Domain Reload Profiling:
	ReloadAssembly (2152ms)
		BeginReloadAssembly (232ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1760ms)
			LoadAssemblies (149ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (322ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1177ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1034ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3117.
Memory consumption went from 137.0 MB to 136.3 MB.
Total: 7.524600 ms (FindLiveObjects: 0.469200 ms CreateObjectMapping: 0.180500 ms MarkObjects: 6.051600 ms  DeleteObjects: 0.821700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015286 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.164 seconds
Domain Reload Profiling:
	ReloadAssembly (2165ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1750ms)
			LoadAssemblies (172ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (316ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1175ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1028ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3132.
Memory consumption went from 137.0 MB to 136.3 MB.
Total: 7.965800 ms (FindLiveObjects: 0.495300 ms CreateObjectMapping: 0.180900 ms MarkObjects: 6.356000 ms  DeleteObjects: 0.931800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 15.52 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (0.7 MB). Loaded Objects now: 3132.
Memory consumption went from 90.6 MB to 89.9 MB.
Total: 9.237800 ms (FindLiveObjects: 0.496400 ms CreateObjectMapping: 0.168000 ms MarkObjects: 7.688100 ms  DeleteObjects: 0.884100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
